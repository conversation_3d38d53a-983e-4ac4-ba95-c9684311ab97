version: '3.8'

services:
  # Shared API Backend
  api:
    build:
      context: .
      target: development
    ports:
      - "3010:5000"
    environment:
      - NODE_ENV=development
      - PORT=5000
      - JWT_SECRET=dev-secret-key
      - CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:3002
    volumes:
      - ./packages/shared-api:/app/packages/shared-api
      - ./packages/shared-types:/app/packages/shared-types
      - ./packages/mock-data:/app/packages/mock-data
      - /app/node_modules
    command: pnpm --filter @travelease/shared-api dev
    networks:
      - travelease-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Super Admin Portal
  web-admin:
    build:
      context: .
      target: development
    ports:
      - "3020:3000"
    environment:
      - VITE_API_URL=http://localhost:3010
      - VITE_APP_ENV=development
    volumes:
      - ./packages/web-admin:/app/packages/web-admin
      - ./packages/shared-ui:/app/packages/shared-ui
      - ./packages/shared-types:/app/packages/shared-types
      - /app/node_modules
    command: pnpm --filter @travelease/web-admin dev --host 0.0.0.0
    depends_on:
      - api
    networks:
      - travelease-network

  # Travel Agent Portal
  web-agent:
    build:
      context: .
      target: development
    ports:
      - "3011:3001"
    environment:
      - VITE_API_URL=http://localhost:3010
      - VITE_APP_ENV=development
    volumes:
      - ./packages/web-agent:/app/packages/web-agent
      - ./packages/shared-ui:/app/packages/shared-ui
      - ./packages/shared-types:/app/packages/shared-types
      - /app/node_modules
    command: pnpm --filter @travelease/web-agent dev --host 0.0.0.0 --port 3001
    depends_on:
      - api
    networks:
      - travelease-network

  # Mobile Consumer App (Expo Web)
  mobile-consumer:
    build:
      context: .
      target: development
    ports:
      - "3012:3002"
    environment:
      - EXPO_PUBLIC_API_URL=http://localhost:3010
      - EXPO_PUBLIC_APP_ENV=development
    volumes:
      - ./packages/mobile-consumer:/app/packages/mobile-consumer
      - ./packages/shared-types:/app/packages/shared-types
      - /app/node_modules
    command: sh -c "cd packages/mobile-consumer && npx expo start --web --port 3002"
    depends_on:
      - api
    networks:
      - travelease-network

  # Production API
  api-prod:
    build:
      context: .
      target: api-production
    ports:
      - "5001:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - JWT_SECRET=prod-secret-key
    networks:
      - travelease-network
    profiles:
      - production

  # Production Web Apps
  web-prod:
    build:
      context: .
      target: web-production
    ports:
      - "8080:80"
    depends_on:
      - api-prod
    networks:
      - travelease-network
    profiles:
      - production

networks:
  travelease-network:
    driver: bridge

volumes:
  node_modules:
    driver: local