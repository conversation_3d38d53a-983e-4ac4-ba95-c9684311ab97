import { Router, Request, Response } from 'express';
import { query, param, validationResult } from 'express-validator';
import { User, UserRole, PaginatedResponse, Consumer } from '@travelease/shared-types';
import { users } from '@travelease/mock-data';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { requireRole } from '../middleware/auth';

const router = Router();

// GET /api/users - Get all users (Super Admin only)
router.get('/', [
  requireRole('super_admin'),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('role').optional().isIn(['super_admin', 'travel_agent', 'consumer']),
  query('search').optional().isString()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const role = req.query.role as UserRole;
  const search = req.query.search as string;

  let filteredUsers = [...users];

  // Filter by role
  if (role) {
    filteredUsers = filteredUsers.filter(user => user.role === role);
  }

  // Filter by search term
  if (search) {
    const searchLower = search.toLowerCase();
    filteredUsers = filteredUsers.filter(user => 
      user.firstName.toLowerCase().includes(searchLower) ||
      user.lastName.toLowerCase().includes(searchLower) ||
      user.email.toLowerCase().includes(searchLower)
    );
  }

  // Pagination
  const total = filteredUsers.length;
  const totalPages = Math.ceil(total / limit);
  const offset = (page - 1) * limit;
  const paginatedUsers = filteredUsers.slice(offset, offset + limit);

  const response: PaginatedResponse<User> = {
    data: paginatedUsers,
    pagination: {
      page,
      limit,
      total,
      totalPages
    }
  };

  res.json({
    success: true,
    ...response
  });
}));

// GET /api/users/:id - Get user by ID
router.get('/:id', [
  param('id').isString().notEmpty()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { id } = req.params;
  const user = users.find(u => u.id === id);

  if (!user) {
    throw createError('User not found', 404);
  }

  // Check permissions - users can view their own profile, admins can view any
  if (req.user?.role !== 'super_admin' && req.user?.id !== id) {
    throw createError('Insufficient permissions', 403);
  }

  res.json({
    success: true,
    data: user
  });
}));

// GET /api/users/agents/stats - Get travel agent statistics (Super Admin only)
router.get('/agents/stats', [
  requireRole('super_admin')
], asyncHandler(async (req: Request, res: Response) => {
  const travelAgents = users.filter(user => user.role === 'travel_agent');
  
  const stats = {
    totalAgents: travelAgents.length,
    activeAgents: travelAgents.filter(agent => agent.isActive).length,
    verifiedAgents: travelAgents.filter(agent => 
      'verificationStatus' in agent && agent.verificationStatus === 'verified'
    ).length,
    pendingVerification: travelAgents.filter(agent => 
      'verificationStatus' in agent && agent.verificationStatus === 'pending'
    ).length,
    averageRating: travelAgents.reduce((sum, agent) => {
      return sum + ('rating' in agent ? agent.rating : 0);
    }, 0) / travelAgents.length || 0
  };

  res.json({
    success: true,
    data: stats
  });
}));

// GET /api/users/consumers/stats - Get consumer statistics (Super Admin only)
router.get('/consumers/stats', [
  requireRole('super_admin')
], asyncHandler(async (req: Request, res: Response) => {
  const consumers = users.filter(user => user.role === 'consumer');
  
  const stats = {
    totalConsumers: consumers.length,
    activeConsumers: consumers.filter(consumer => consumer.isActive).length,
    newThisMonth: consumers.filter(consumer => {
      const createdAt = new Date(consumer.createdAt);
      const now = new Date();
      return createdAt.getMonth() === now.getMonth() && 
             createdAt.getFullYear() === now.getFullYear();
    }).length,
    preferredTravelStyles: consumers.reduce((acc, consumer) => {
      const consumerUser = consumer as Consumer;
      if (consumerUser.preferences) {
        const style = consumerUser.preferences.travelStyle;
        acc[style] = (acc[style] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>)
  };

  res.json({
    success: true,
    data: stats
  });
}));

// PUT /api/users/:id/status - Update user status (Super Admin only)
router.put('/:id/status', [
  requireRole('super_admin'),
  param('id').isString().notEmpty()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { id } = req.params;
  const { isActive } = req.body;

  if (typeof isActive !== 'boolean') {
    throw createError('isActive must be a boolean', 400);
  }

  const userIndex = users.findIndex(u => u.id === id);
  if (userIndex === -1) {
    throw createError('User not found', 404);
  }

  // Update user status (in real app, this would update the database)
  users[userIndex] = {
    ...users[userIndex],
    isActive,
    updatedAt: new Date()
  };

  res.json({
    success: true,
    data: users[userIndex],
    message: `User ${isActive ? 'activated' : 'deactivated'} successfully`
  });
}));

// GET /api/users/profile - Get current user profile
router.get('/profile', asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    throw createError('User not found', 404);
  }

  res.json({
    success: true,
    data: req.user
  });
}));

export default router;