import { Router, Request, Response } from 'express';
import { query, param, body, validationResult } from 'express-validator';
import { Notification, NotificationType, PaginatedResponse } from '@travelease/shared-types';
import { notifications } from '@travelease/mock-data';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { authMiddleware, requireRole } from '../middleware/auth';

const router = Router();

// GET /api/notifications - Get user notifications
router.get('/', [
  authMiddleware,
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 50 }),
  query('type').optional().isIn(['booking_confirmation', 'payment_reminder', 'trip_update', 'weather_alert', 'document_reminder', 'system_announcement']),
  query('isRead').optional().isBoolean(),
  query('priority').optional().isIn(['low', 'medium', 'high', 'urgent'])
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const type = req.query.type as NotificationType;
  const isRead = req.query.isRead === 'true';
  const priority = req.query.priority as string;

  // Filter notifications for current user
  let userNotifications = notifications.filter(notification => 
    notification.userId === req.user?.id
  );

  // Apply filters
  if (type) {
    userNotifications = userNotifications.filter(notification => notification.type === type);
  }

  if (req.query.isRead !== undefined) {
    userNotifications = userNotifications.filter(notification => notification.isRead === isRead);
  }

  if (priority) {
    userNotifications = userNotifications.filter(notification => notification.priority === priority);
  }

  // Sort by creation date (newest first), then by priority
  userNotifications.sort((a, b) => {
    const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
    const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder];
    const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder];
    
    if (aPriority !== bPriority) {
      return bPriority - aPriority; // Higher priority first
    }
    
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  // Pagination
  const total = userNotifications.length;
  const totalPages = Math.ceil(total / limit);
  const offset = (page - 1) * limit;
  const paginatedNotifications = userNotifications.slice(offset, offset + limit);

  const response: PaginatedResponse<Notification> = {
    data: paginatedNotifications,
    pagination: {
      page,
      limit,
      total,
      totalPages
    }
  };

  res.json({
    success: true,
    ...response
  });
}));

// GET /api/notifications/unread/count - Get unread notification count
router.get('/unread/count', [
  authMiddleware
], asyncHandler(async (req: Request, res: Response) => {
  const unreadCount = notifications.filter(notification => 
    notification.userId === req.user?.id && !notification.isRead
  ).length;

  res.json({
    success: true,
    data: { count: unreadCount }
  });
}));

// GET /api/notifications/:id - Get notification by ID
router.get('/:id', [
  authMiddleware,
  param('id').isString().notEmpty()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { id } = req.params;
  const notification = notifications.find(n => n.id === id);

  if (!notification) {
    throw createError('Notification not found', 404);
  }

  // Check if user owns this notification
  if (notification.userId !== req.user?.id) {
    throw createError('Insufficient permissions', 403);
  }

  res.json({
    success: true,
    data: notification
  });
}));

// PUT /api/notifications/:id/read - Mark notification as read
router.put('/:id/read', [
  authMiddleware,
  param('id').isString().notEmpty()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { id } = req.params;
  const notificationIndex = notifications.findIndex(n => n.id === id);

  if (notificationIndex === -1) {
    throw createError('Notification not found', 404);
  }

  const notification = notifications[notificationIndex];

  // Check if user owns this notification
  if (notification.userId !== req.user?.id) {
    throw createError('Insufficient permissions', 403);
  }

  // Mark as read
  notifications[notificationIndex] = {
    ...notification,
    isRead: true,
    readAt: new Date()
  };

  res.json({
    success: true,
    data: notifications[notificationIndex],
    message: 'Notification marked as read'
  });
}));

// PUT /api/notifications/read-all - Mark all notifications as read
router.put('/read-all', [
  authMiddleware
], asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user?.id;
  let updatedCount = 0;

  // Mark all user's unread notifications as read
  for (let i = 0; i < notifications.length; i++) {
    if (notifications[i].userId === userId && !notifications[i].isRead) {
      notifications[i] = {
        ...notifications[i],
        isRead: true,
        readAt: new Date()
      };
      updatedCount++;
    }
  }

  res.json({
    success: true,
    data: { updatedCount },
    message: `${updatedCount} notifications marked as read`
  });
}));

// POST /api/notifications - Create notification (Super Admin only)
router.post('/', [
  requireRole('super_admin'),
  body('userId').isString().notEmpty(),
  body('type').isIn(['booking_confirmation', 'payment_reminder', 'trip_update', 'weather_alert', 'document_reminder', 'system_announcement']),
  body('title').isString().isLength({ min: 1, max: 200 }),
  body('message').isString().isLength({ min: 1, max: 1000 }),
  body('priority').isIn(['low', 'medium', 'high', 'urgent']),
  body('actionUrl').optional().isURL()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { userId, type, title, message, priority, actionUrl } = req.body;

  const newNotification: Notification = {
    id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    userId,
    type: type as NotificationType,
    title,
    message,
    isRead: false,
    priority,
    actionUrl,
    createdAt: new Date()
  };

  // Add to mock data (in real app, save to database)
  notifications.push(newNotification);

  res.status(201).json({
    success: true,
    data: newNotification,
    message: 'Notification created successfully'
  });
}));

// POST /api/notifications/broadcast - Broadcast notification to all users (Super Admin only)
router.post('/broadcast', [
  requireRole('super_admin'),
  body('type').isIn(['system_announcement', 'weather_alert']),
  body('title').isString().isLength({ min: 1, max: 200 }),
  body('message').isString().isLength({ min: 1, max: 1000 }),
  body('priority').isIn(['low', 'medium', 'high', 'urgent']),
  body('actionUrl').optional().isURL(),
  body('userRoles').optional().isArray()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { type, title, message, priority, actionUrl, userRoles } = req.body;

  // Get all users or filter by roles
  const { users } = await import('@travelease/mock-data');
  let targetUsers = users;
  
  if (userRoles && userRoles.length > 0) {
    targetUsers = users.filter(user => userRoles.includes(user.role));
  }

  const broadcastNotifications: Notification[] = targetUsers.map(user => ({
    id: `notif_${Date.now()}_${user.id}_${Math.random().toString(36).substr(2, 9)}`,
    userId: user.id,
    type: type as NotificationType,
    title,
    message,
    isRead: false,
    priority,
    actionUrl,
    createdAt: new Date()
  }));

  // Add all notifications to mock data
  notifications.push(...broadcastNotifications);

  res.status(201).json({
    success: true,
    data: {
      notificationsSent: broadcastNotifications.length,
      targetUsers: targetUsers.length
    },
    message: `Broadcast notification sent to ${broadcastNotifications.length} users`
  });
}));

// DELETE /api/notifications/:id - Delete notification
router.delete('/:id', [
  authMiddleware,
  param('id').isString().notEmpty()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { id } = req.params;
  const notificationIndex = notifications.findIndex(n => n.id === id);

  if (notificationIndex === -1) {
    throw createError('Notification not found', 404);
  }

  const notification = notifications[notificationIndex];

  // Check permissions - users can delete their own notifications, super admin can delete any
  if (req.user?.role !== 'super_admin' && notification.userId !== req.user?.id) {
    throw createError('Insufficient permissions', 403);
  }

  // Remove notification
  notifications.splice(notificationIndex, 1);

  res.json({
    success: true,
    message: 'Notification deleted successfully'
  });
}));

export default router;