import { Router, Request, Response } from 'express';
import { query, validationResult } from 'express-validator';
import { AnalyticsData } from '@travelease/shared-types';
import { analytics, bookings, travelPackages, users } from '@travelease/mock-data';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { requireRole } from '../middleware/auth';

const router = Router();

// GET /api/analytics/overview - Get general analytics overview
router.get('/overview', [
  requireRole(['super_admin', 'travel_agent'])
], asyncHandler(async (req: Request, res: Response) => {
  let relevantBookings = bookings;
  let relevantPackages = travelPackages;

  // Filter data for travel agents
  if (req.user?.role === 'travel_agent') {
    relevantBookings = bookings.filter(booking => booking.agentId === req.user?.id);
    relevantPackages = travelPackages.filter(pkg => pkg.agentId === req.user?.id);
  }

  const overview = {
    totalBookings: relevantBookings.length,
    totalRevenue: relevantBookings
      .filter(b => b.paymentStatus === 'paid')
      .reduce((sum, b) => sum + b.totalAmount, 0),
    totalPackages: relevantPackages.length,
    activePackages: relevantPackages.filter(pkg => pkg.status === 'published').length,
    averageBookingValue: relevantBookings.length > 0 
      ? relevantBookings.reduce((sum, b) => sum + b.totalAmount, 0) / relevantBookings.length 
      : 0,
    conversionRate: relevantPackages.length > 0 
      ? (relevantBookings.length / relevantPackages.length) * 100 
      : 0
  };

  res.json({
    success: true,
    data: overview
  });
}));

// GET /api/analytics/bookings - Get booking analytics
router.get('/bookings', [
  requireRole(['super_admin', 'travel_agent']),
  query('period').optional().isIn(['7d', '30d', '90d', '1y']),
  query('groupBy').optional().isIn(['day', 'week', 'month'])
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const period = req.query.period as string || '30d';
  const groupBy = req.query.groupBy as string || 'day';

  let relevantBookings = bookings;

  // Filter data for travel agents
  if (req.user?.role === 'travel_agent') {
    relevantBookings = bookings.filter(booking => booking.agentId === req.user?.id);
  }

  // Filter by period
  const now = new Date();
  const periodDays = {
    '7d': 7,
    '30d': 30,
    '90d': 90,
    '1y': 365
  };
  
  const startDate = new Date(now.getTime() - (periodDays[period as keyof typeof periodDays] * 24 * 60 * 60 * 1000));
  const filteredBookings = relevantBookings.filter(booking => 
    new Date(booking.bookingDate) >= startDate
  );

  // Group bookings by time period
  const groupedData = filteredBookings.reduce((acc, booking) => {
    const date = new Date(booking.bookingDate);
    let key: string;

    switch (groupBy) {
      case 'week':
        const weekStart = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay());
        key = weekStart.toISOString().split('T')[0];
        break;
      case 'month':
        const month = date.getMonth() + 1;
        key = `${date.getFullYear()}-${month < 10 ? '0' + month : month}`;
        break;
      default: // day
        key = date.toISOString().split('T')[0];
    }

    if (!acc[key]) {
      acc[key] = {
        period: key,
        bookings: 0,
        revenue: 0,
        cancelledBookings: 0
      };
    }

    acc[key].bookings += 1;
    if (booking.paymentStatus === 'paid') {
      acc[key].revenue += booking.totalAmount;
    }
    if (booking.status === 'cancelled') {
      acc[key].cancelledBookings += 1;
    }

    return acc;
  }, {} as Record<string, any>);

  const chartData = Object.keys(groupedData).map(key => groupedData[key]).sort((a: any, b: any) => 
    a.period.localeCompare(b.period)
  );

  res.json({
    success: true,
    data: {
      period,
      groupBy,
      chartData,
      summary: {
        totalBookings: filteredBookings.length,
        totalRevenue: filteredBookings
          .filter(b => b.paymentStatus === 'paid')
          .reduce((sum, b) => sum + b.totalAmount, 0),
        cancelledBookings: filteredBookings.filter(b => b.status === 'cancelled').length,
        averageBookingValue: filteredBookings.length > 0 
          ? filteredBookings.reduce((sum, b) => sum + b.totalAmount, 0) / filteredBookings.length 
          : 0
      }
    }
  });
}));

// GET /api/analytics/packages - Get package analytics
router.get('/packages', [
  requireRole(['super_admin', 'travel_agent'])
], asyncHandler(async (req: Request, res: Response) => {
  let relevantPackages = travelPackages;
  let relevantBookings = bookings;

  // Filter data for travel agents
  if (req.user?.role === 'travel_agent') {
    relevantPackages = travelPackages.filter(pkg => pkg.agentId === req.user?.id);
    relevantBookings = bookings.filter(booking => booking.agentId === req.user?.id);
  }

  // Package performance by category
  const categoryStats = relevantPackages.reduce((acc, pkg) => {
    const category = pkg.category;
    const packageBookings = relevantBookings.filter(b => b.packageId === pkg.id);
    const revenue = packageBookings
      .filter(b => b.paymentStatus === 'paid')
      .reduce((sum, b) => sum + b.totalAmount, 0);

    if (!acc[category]) {
      acc[category] = {
        category,
        packages: 0,
        bookings: 0,
        revenue: 0,
        averagePrice: 0
      };
    }

    acc[category].packages += 1;
    acc[category].bookings += packageBookings.length;
    acc[category].revenue += revenue;
    
    return acc;
  }, {} as Record<string, any>);

  // Calculate average prices
  Object.keys(categoryStats).forEach(category => {
    const categoryPackages = relevantPackages.filter(pkg => pkg.category === category);
    const totalPrice = categoryPackages.reduce((sum, pkg) => {
      return sum + (pkg.pricing[0]?.basePrice || 0);
    }, 0);
    categoryStats[category].averagePrice = categoryPackages.length > 0 
      ? totalPrice / categoryPackages.length 
      : 0;
  });

  // Top performing packages
  const packagePerformance = relevantPackages.map(pkg => {
    const packageBookings = relevantBookings.filter(b => b.packageId === pkg.id);
    const revenue = packageBookings
      .filter(b => b.paymentStatus === 'paid')
      .reduce((sum, b) => sum + b.totalAmount, 0);

    return {
      id: pkg.id,
      title: pkg.title,
      category: pkg.category,
      bookings: packageBookings.length,
      revenue,
      basePrice: pkg.pricing[0]?.basePrice || 0,
      conversionRate: packageBookings.length > 0 ? (packageBookings.length / 100) * 100 : 0 // Mock view count
    };
  }).sort((a, b) => b.revenue - a.revenue).slice(0, 10);

  res.json({
    success: true,
    data: {
      categoryStats: Object.keys(categoryStats).map(key => categoryStats[key]),
      topPackages: packagePerformance,
      summary: {
        totalPackages: relevantPackages.length,
        publishedPackages: relevantPackages.filter(pkg => pkg.status === 'published').length,
        draftPackages: relevantPackages.filter(pkg => pkg.status === 'draft').length,
        averagePackagePrice: relevantPackages.length > 0 
          ? relevantPackages.reduce((sum, pkg) => sum + (pkg.pricing[0]?.basePrice || 0), 0) / relevantPackages.length 
          : 0
      }
    }
  });
}));

// GET /api/analytics/users - Get user analytics (Super Admin only)
router.get('/users', [
  requireRole('super_admin')
], asyncHandler(async (req: Request, res: Response) => {
  const userStats = {
    totalUsers: users.length,
    activeUsers: users.filter(user => user.isActive).length,
    usersByRole: {
      superAdmins: users.filter(user => user.role === 'super_admin').length,
      travelAgents: users.filter(user => user.role === 'travel_agent').length,
      consumers: users.filter(user => user.role === 'consumer').length
    },
    newUsersThisMonth: users.filter(user => {
      const createdAt = new Date(user.createdAt);
      const now = new Date();
      return createdAt.getMonth() === now.getMonth() && 
             createdAt.getFullYear() === now.getFullYear();
    }).length,
    userGrowth: users.reduce((acc, user) => {
      const date = new Date(user.createdAt);
      const month = date.getMonth() + 1;
      const monthKey = `${date.getFullYear()}-${month < 10 ? '0' + month : month}`;
      
      if (!acc[monthKey]) {
        acc[monthKey] = { month: monthKey, newUsers: 0 };
      }
      acc[monthKey].newUsers += 1;
      
      return acc;
    }, {} as Record<string, any>)
  };

  const userGrowthData = Object.keys(userStats.userGrowth)
    .map(key => userStats.userGrowth[key])
    .sort((a: any, b: any) => a.month.localeCompare(b.month))
    .slice(-12); // Last 12 months

  res.json({
    success: true,
    data: {
      ...userStats,
      userGrowth: userGrowthData
    }
  });
}));

// GET /api/analytics/revenue - Get revenue analytics
router.get('/revenue', [
  requireRole(['super_admin', 'travel_agent']),
  query('period').optional().isIn(['7d', '30d', '90d', '1y']),
  query('groupBy').optional().isIn(['day', 'week', 'month'])
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const period = req.query.period as string || '30d';
  const groupBy = req.query.groupBy as string || 'day';

  let relevantBookings = bookings.filter(b => b.paymentStatus === 'paid');

  // Filter data for travel agents
  if (req.user?.role === 'travel_agent') {
    relevantBookings = relevantBookings.filter(booking => booking.agentId === req.user?.id);
  }

  // Filter by period
  const now = new Date();
  const periodDays = {
    '7d': 7,
    '30d': 30,
    '90d': 90,
    '1y': 365
  };
  
  const startDate = new Date(now.getTime() - (periodDays[period as keyof typeof periodDays] * 24 * 60 * 60 * 1000));
  const filteredBookings = relevantBookings.filter(booking => 
    new Date(booking.bookingDate) >= startDate
  );

  // Group revenue by time period
  const revenueData = filteredBookings.reduce((acc, booking) => {
    const date = new Date(booking.bookingDate);
    let key: string;

    switch (groupBy) {
      case 'week':
        const weekStart = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay());
        key = weekStart.toISOString().split('T')[0];
        break;
      case 'month':
        const month = date.getMonth() + 1;
        key = `${date.getFullYear()}-${month < 10 ? '0' + month : month}`;
        break;
      default: // day
        key = date.toISOString().split('T')[0];
    }

    if (!acc[key]) {
      acc[key] = {
        period: key,
        revenue: 0,
        bookings: 0,
        averageOrderValue: 0
      };
    }

    acc[key].revenue += booking.totalAmount;
    acc[key].bookings += 1;
    acc[key].averageOrderValue = acc[key].revenue / acc[key].bookings;

    return acc;
  }, {} as Record<string, any>);

  const chartData = Object.keys(revenueData).map(key => revenueData[key]).sort((a: any, b: any) => 
    a.period.localeCompare(b.period)
  );

  const totalRevenue = filteredBookings.reduce((sum, b) => sum + b.totalAmount, 0);
  const previousPeriodStart = new Date(startDate.getTime() - (periodDays[period as keyof typeof periodDays] * 24 * 60 * 60 * 1000));
  const previousPeriodBookings = relevantBookings.filter(booking => {
    const bookingDate = new Date(booking.bookingDate);
    return bookingDate >= previousPeriodStart && bookingDate < startDate;
  });
  const previousRevenue = previousPeriodBookings.reduce((sum, b) => sum + b.totalAmount, 0);
  const revenueGrowth = previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0;

  res.json({
    success: true,
    data: {
      period,
      groupBy,
      chartData,
      summary: {
        totalRevenue,
        previousPeriodRevenue: previousRevenue,
        revenueGrowth,
        averageOrderValue: filteredBookings.length > 0 
          ? totalRevenue / filteredBookings.length 
          : 0,
        totalTransactions: filteredBookings.length
      }
    }
  });
}));

export default router;