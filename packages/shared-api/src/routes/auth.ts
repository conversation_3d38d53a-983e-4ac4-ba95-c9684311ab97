import { Router, Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { User, LoginCredentials, AuthToken, AuthUser } from '@travelease/shared-types';
import { users } from '@travelease/mock-data';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { authMiddleware } from '../middleware/auth';

const router = Router();

// Mock password validation (in real app, use bcrypt)
const validatePassword = (password: string, user: User): boolean => {
  // Mock validation - accept any password for demo
  // In real app: return bcrypt.compareSync(password, user.hashedPassword);
  return password.length >= 6;
};

// Generate mock JWT token
const generateMockToken = (user: User): string => {
  // Mock token format: "mock_token_userId"
  // In real app: return jwt.sign({ userId: user.id, role: user.role }, JWT_SECRET);
  return `mock_token_${user.id}`;
};

// POST /api/auth/login
router.post('/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 6 })
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { email, password }: LoginCredentials = req.body;

  // Find user by email
  const user = users.find((u: User) => u.email.toLowerCase() === email.toLowerCase());

  if (!user) {
    throw createError('Invalid email or password', 401);
  }

  if (!user.isActive) {
    throw createError('Account is deactivated', 403);
  }

  if (!validatePassword(password, user)) {
    throw createError('Invalid email or password', 401);
  }

  // Generate token
  const token = generateMockToken(user);
  const expiresIn = 24 * 60 * 60; // 24 hours in seconds
  const expiresAt = new Date(Date.now() + expiresIn * 1000);

  const authToken: AuthToken = {
    accessToken: token,
    refreshToken: `refresh_${token}`,
    expiresIn,
    tokenType: 'Bearer'
  };

  const authUser: AuthUser = {
    id: user.id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    role: user.role,
    avatar: user.avatar
  };

  res.json({
    success: true,
    data: {
      user: authUser,
      token: authToken
    },
    message: 'Login successful'
  });
}));

// POST /api/auth/logout
router.post('/logout', authMiddleware, asyncHandler(async (req: Request, res: Response) => {
  // In a real app, you would invalidate the token here
  // For mock implementation, we just return success
  res.json({
    success: true,
    message: 'Logout successful'
  });
}));

// GET /api/auth/me
router.get('/me', authMiddleware, asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    throw createError('User not found', 404);
  }

  const authUser: AuthUser = {
    id: req.user.id,
    email: req.user.email,
    firstName: req.user.firstName,
    lastName: req.user.lastName,
    role: req.user.role,
    avatar: req.user.avatar
  };

  res.json({
    success: true,
    data: authUser
  });
}));

// POST /api/auth/refresh
router.post('/refresh', authMiddleware, asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    throw createError('User not found', 404);
  }

  // Generate new token
  const token = generateMockToken(req.user);
  const expiresIn = 24 * 60 * 60; // 24 hours in seconds
  const expiresAt = new Date(Date.now() + expiresIn * 1000);

  const authToken: AuthToken = {
    accessToken: token,
    refreshToken: `refresh_${token}`,
    expiresIn,
    tokenType: 'Bearer'
  };

  res.json({
    success: true,
    data: {
      token: authToken
    },
    message: 'Token refreshed successfully'
  });
}));

// GET /api/auth/demo-users
// Endpoint to get demo login credentials for testing
router.get('/demo-users', asyncHandler(async (req: Request, res: Response) => {
  const demoUsers = [
    users.find((u: User) => u.role === 'super_admin'),
    users.find((u: User) => u.role === 'travel_agent'),
    users.find((u: User) => u.role === 'consumer')
  ].filter((user): user is User => user !== undefined).map((user: User) => ({
    email: user!.email,
    password: 'demo123', // Mock password for demo
    role: user!.role,
    name: `${user!.firstName} ${user!.lastName}`
  }));

  res.json({
    success: true,
    data: demoUsers,
    message: 'Demo user credentials (use password: demo123 for all)'
  });
}));

export default router;