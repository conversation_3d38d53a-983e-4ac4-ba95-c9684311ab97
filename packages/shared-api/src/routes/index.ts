import { Router, Request, Response } from 'express';
import authRoutes from './auth';
import userRoutes from './users';
import packageRoutes from './packages';
import bookingRoutes from './bookings';
import notificationRoutes from './notifications';
import analyticsRoutes from './analytics';

const router = Router();

// Mount all route modules
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/packages', packageRoutes);
router.use('/bookings', bookingRoutes);
router.use('/notifications', notificationRoutes);
router.use('/analytics', analyticsRoutes);

// Health check endpoint
router.get('/health', (req: Request, res: Response) => {
  res.json({
    success: true,
    message: 'TravelEase API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

export default router;