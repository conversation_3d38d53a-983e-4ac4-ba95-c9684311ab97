import { Router, Request, Response } from 'express';
import { query, param, body, validationResult } from 'express-validator';
import { TravelPackage, PackageCategory, PaginatedResponse } from '@travelease/shared-types';
import { travelPackages } from '@travelease/mock-data';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { requireRole, optionalAuth } from '../middleware/auth';

const router = Router();

// GET /api/packages - Get all packages (public endpoint with optional auth)
router.get('/', [
  optionalAuth,
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 50 }),
  query('category').optional().isIn(['adventure', 'cultural', 'relaxation', 'business', 'family', 'luxury', 'budget']),
  query('destination').optional().isString(),
  query('minPrice').optional().isFloat({ min: 0 }),
  query('maxPrice').optional().isFloat({ min: 0 }),
  query('duration').optional().isInt({ min: 1 }),
  query('search').optional().isString(),
  query('featured').optional().isBoolean()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 12;
  const category = req.query.category as PackageCategory;
  const destination = req.query.destination as string;
  const minPrice = parseFloat(req.query.minPrice as string);
  const maxPrice = parseFloat(req.query.maxPrice as string);
  const duration = parseInt(req.query.duration as string);
  const search = req.query.search as string;
  const featured = req.query.featured === 'true';

  let filteredPackages = [...travelPackages];

  // Filter by category
  if (category) {
    filteredPackages = filteredPackages.filter(pkg => pkg.category === category);
  }

  // Filter by destination
  if (destination) {
    const destLower = destination.toLowerCase();
    filteredPackages = filteredPackages.filter(pkg => 
      pkg.destination.toLowerCase().includes(destLower)
    );
  }

  // Filter by price range
  if (!isNaN(minPrice)) {
    filteredPackages = filteredPackages.filter(pkg => 
      pkg.pricing.some(tier => tier.basePrice >= minPrice)
    );
  }
  if (!isNaN(maxPrice)) {
    filteredPackages = filteredPackages.filter(pkg => 
      pkg.pricing.some(tier => tier.basePrice <= maxPrice)
    );
  }

  // Filter by duration
  if (duration) {
    filteredPackages = filteredPackages.filter(pkg => pkg.duration === duration);
  }

  // Filter by search term
  if (search) {
    const searchLower = search.toLowerCase();
    filteredPackages = filteredPackages.filter(pkg => 
      pkg.title.toLowerCase().includes(searchLower) ||
      pkg.description.toLowerCase().includes(searchLower) ||
      pkg.destination.toLowerCase().includes(searchLower)
    );
  }

  // Filter by featured (assuming featured is part of status or a custom field)
  if (featured) {
    filteredPackages = filteredPackages.filter(pkg => pkg.status === 'published');
  }

  // Sort by creation date
  filteredPackages.sort((a, b) => {
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  // Pagination
  const total = filteredPackages.length;
  const totalPages = Math.ceil(total / limit);
  const offset = (page - 1) * limit;
  const paginatedPackages = filteredPackages.slice(offset, offset + limit);

  const response: PaginatedResponse<TravelPackage> = {
    data: paginatedPackages,
    pagination: {
      page,
      limit,
      total,
      totalPages
    }
  };

  res.json({
    success: true,
    ...response
  });
}));

// GET /api/packages/:id - Get package by ID (public endpoint)
router.get('/:id', [
  param('id').isString().notEmpty()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { id } = req.params;
  const travelPackage = travelPackages.find((pkg: TravelPackage) => pkg.id === id);

  if (!travelPackage) {
    throw createError('Package not found', 404);
  }

  res.json({
    success: true,
    data: travelPackage
  });
}));

// GET /api/packages/categories/stats - Get package statistics by category
router.get('/categories/stats', asyncHandler(async (req: Request, res: Response) => {
  const categoryStats = travelPackages.reduce((acc: Record<string, any>, pkg: TravelPackage) => {
    const category = pkg.category;
    if (!acc[category]) {
      acc[category] = {
        count: 0,
        averagePrice: 0,
        averageRating: 0,
        totalPrice: 0,
        totalRating: 0
      };
    }
    
    acc[category].count += 1;
    const basePrice = pkg.pricing[0]?.basePrice || 0;
    acc[category].totalPrice += basePrice;
    acc[category].totalRating += 4.5; // Mock rating since not in interface
    acc[category].averagePrice = acc[category].totalPrice / acc[category].count;
    acc[category].averageRating = acc[category].totalRating / acc[category].count;
    
    return acc;
  }, {} as Record<string, any>);

  // Clean up the stats object
  Object.keys(categoryStats).forEach(category => {
    delete categoryStats[category].totalPrice;
    delete categoryStats[category].totalRating;
    categoryStats[category].averagePrice = Math.round(categoryStats[category].averagePrice * 100) / 100;
    categoryStats[category].averageRating = Math.round(categoryStats[category].averageRating * 10) / 10;
  });

  res.json({
    success: true,
    data: categoryStats
  });
}));

// GET /api/packages/featured - Get featured packages
router.get('/featured', [
  query('limit').optional().isInt({ min: 1, max: 20 })
], asyncHandler(async (req: Request, res: Response) => {
  const limit = parseInt(req.query.limit as string) || 6;
  
  const featuredPackages = travelPackages
    .filter((pkg: TravelPackage) => pkg.status === 'published')
    .sort((a: TravelPackage, b: TravelPackage) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, limit);

  res.json({
    success: true,
    data: featuredPackages
  });
}));

// POST /api/packages - Create new package (Travel Agent and Super Admin only)
router.post('/', [
  requireRole(['travel_agent', 'super_admin']),
  body('title').isString().isLength({ min: 3, max: 200 }),
  body('description').isString().isLength({ min: 10, max: 2000 }),
  body('category').isIn(['adventure', 'cultural', 'relaxation', 'business', 'family', 'luxury', 'budget']),
  body('duration').isInt({ min: 1, max: 365 }),
  body('pricing').isArray({ min: 1 }),
  body('destination.city').isString().notEmpty(),
  body('destination.country').isString().notEmpty()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  // In a real application, this would save to database
  const newPackage: TravelPackage = {
    id: `pkg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    ...req.body,
    agentId: req.user?.id || '',
    status: 'draft',
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // Add to mock data (in real app, save to database)
  travelPackages.push(newPackage);

  res.status(201).json({
    success: true,
    data: newPackage,
    message: 'Package created successfully'
  });
}));

// PUT /api/packages/:id - Update package (Travel Agent and Super Admin only)
router.put('/:id', [
  requireRole(['travel_agent', 'super_admin']),
  param('id').isString().notEmpty(),
  body('title').optional().isString().isLength({ min: 3, max: 200 }),
  body('description').optional().isString().isLength({ min: 10, max: 2000 }),
  body('category').optional().isIn(['adventure', 'cultural', 'relaxation', 'business', 'family', 'luxury', 'budget']),
  body('duration').optional().isInt({ min: 1, max: 365 }),
  body('pricing').optional().isArray({ min: 1 })
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { id } = req.params;
  const packageIndex = travelPackages.findIndex((pkg: TravelPackage) => pkg.id === id);
  
  if (packageIndex === -1) {
    throw createError('Package not found', 404);
  }

  const existingPackage = travelPackages[packageIndex];
  
  // Check if user can edit this package
  if (req.user?.role !== 'super_admin' && existingPackage.agentId !== req.user?.id) {
    throw createError('Insufficient permissions', 403);
  }

  // Update package (in real app, update database)
  travelPackages[packageIndex] = {
    ...existingPackage,
    ...req.body,
    updatedAt: new Date()
  };

  res.json({
    success: true,
    data: travelPackages[packageIndex],
    message: 'Package updated successfully'
  });
}));

// DELETE /api/packages/:id - Delete package (Travel Agent and Super Admin only)
router.delete('/:id', [
  requireRole(['travel_agent', 'super_admin']),
  param('id').isString().notEmpty()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { id } = req.params;
  const packageIndex = travelPackages.findIndex((pkg: TravelPackage) => pkg.id === id);
  
  if (packageIndex === -1) {
    throw createError('Package not found', 404);
  }

  const existingPackage = travelPackages[packageIndex];
  
  // Check if user can delete this package
  if (req.user?.role !== 'super_admin' && existingPackage.agentId !== req.user?.id) {
    throw createError('Insufficient permissions', 403);
  }

  // Remove package (in real app, delete from database)
  travelPackages.splice(packageIndex, 1);

  res.json({
    success: true,
    message: 'Package deleted successfully'
  });
}));

export default router;