import { Router, Request, Response } from 'express';
import { query, param, body, validationResult } from 'express-validator';
import { Booking, BookingStatus, PaymentStatus, PaginatedResponse } from '@travelease/shared-types';
import { bookings, travelPackages, users } from '@travelease/mock-data';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { requireRole, authMiddleware } from '../middleware/auth';

const router = Router();

// GET /api/bookings - Get bookings (role-based access)
router.get('/', [
  authMiddleware,
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['pending', 'confirmed', 'cancelled', 'completed', 'in_progress']),
  query('paymentStatus').optional().isIn(['pending', 'partial', 'paid', 'refunded', 'failed']),
  query('agentId').optional().isString(),
  query('consumerId').optional().isString(),
  query('packageId').optional().isString(),
  query('search').optional().isString()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const status = req.query.status as BookingStatus;
  const paymentStatus = req.query.paymentStatus as PaymentStatus;
  const agentId = req.query.agentId as string;
  const consumerId = req.query.consumerId as string;
  const packageId = req.query.packageId as string;
  const search = req.query.search as string;

  let filteredBookings = [...bookings];

  // Role-based filtering
  if (req.user?.role === 'travel_agent') {
    filteredBookings = filteredBookings.filter(booking => booking.agentId === req.user?.id);
  } else if (req.user?.role === 'consumer') {
    filteredBookings = filteredBookings.filter(booking => booking.consumerId === req.user?.id);
  }
  // Super admin can see all bookings

  // Apply filters
  if (status) {
    filteredBookings = filteredBookings.filter(booking => booking.status === status);
  }

  if (paymentStatus) {
    filteredBookings = filteredBookings.filter(booking => booking.paymentStatus === paymentStatus);
  }

  if (agentId && req.user?.role === 'super_admin') {
    filteredBookings = filteredBookings.filter(booking => booking.agentId === agentId);
  }

  if (consumerId && req.user?.role !== 'consumer') {
    filteredBookings = filteredBookings.filter(booking => booking.consumerId === consumerId);
  }

  if (packageId) {
    filteredBookings = filteredBookings.filter(booking => booking.packageId === packageId);
  }

  if (search) {
    const searchLower = search.toLowerCase();
    filteredBookings = filteredBookings.filter(booking => {
      const travelPackage = travelPackages.find(pkg => pkg.id === booking.packageId);
      const consumer = users.find(user => user.id === booking.consumerId);
      return (
        booking.id.toLowerCase().includes(searchLower) ||
        (travelPackage?.title.toLowerCase().includes(searchLower)) ||
        (consumer?.firstName.toLowerCase().includes(searchLower)) ||
        (consumer?.lastName.toLowerCase().includes(searchLower))
      );
    });
  }

  // Sort by booking date (newest first)
  filteredBookings.sort((a, b) => 
    new Date(b.bookingDate).getTime() - new Date(a.bookingDate).getTime()
  );

  // Pagination
  const total = filteredBookings.length;
  const totalPages = Math.ceil(total / limit);
  const offset = (page - 1) * limit;
  const paginatedBookings = filteredBookings.slice(offset, offset + limit);

  const response: PaginatedResponse<Booking> = {
    data: paginatedBookings,
    pagination: {
      page,
      limit,
      total,
      totalPages
    }
  };

  res.json({
    success: true,
    ...response
  });
}));

// GET /api/bookings/:id - Get booking by ID
router.get('/:id', [
  authMiddleware,
  param('id').isString().notEmpty()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { id } = req.params;
  const booking = bookings.find(b => b.id === id);

  if (!booking) {
    throw createError('Booking not found', 404);
  }

  // Check permissions
  if (req.user?.role === 'consumer' && booking.consumerId !== req.user.id) {
    throw createError('Insufficient permissions', 403);
  }
  if (req.user?.role === 'travel_agent' && booking.agentId !== req.user.id) {
    throw createError('Insufficient permissions', 403);
  }

  res.json({
    success: true,
    data: booking
  });
}));

// POST /api/bookings - Create new booking (Consumer only)
router.post('/', [
  requireRole('consumer'),
  body('packageId').isString().notEmpty(),
  body('travelers').isArray({ min: 1 }),
  body('travelDate').isISO8601(),
  body('specialRequests').optional().isString()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { packageId, travelers, travelDate, specialRequests } = req.body;

  // Verify package exists
  const travelPackage = travelPackages.find(pkg => pkg.id === packageId);
  if (!travelPackage) {
    throw createError('Package not found', 404);
  }

  // Calculate total amount (using first pricing tier as base)
  const basePrice = travelPackage.pricing[0]?.basePrice || 0;
  const totalAmount = basePrice * travelers.length;

  const newBooking: Booking = {
    id: `booking_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    packageId,
    consumerId: req.user?.id || '',
    agentId: travelPackage.agentId,
    status: 'pending',
    travelers: travelers.map((traveler: any, index: number) => ({
      id: `traveler_${Date.now()}_${index}`,
      ...traveler
    })),
    totalAmount,
    currency: travelPackage.pricing[0]?.currency || 'USD',
    paymentStatus: 'pending',
    bookingDate: new Date(),
    travelDate: new Date(travelDate),
    specialRequests,
    documents: [],
    notifications: [],
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // Add to mock data (in real app, save to database)
  bookings.push(newBooking);

  res.status(201).json({
    success: true,
    data: newBooking,
    message: 'Booking created successfully'
  });
}));

// PUT /api/bookings/:id/status - Update booking status
router.put('/:id/status', [
  authMiddleware,
  param('id').isString().notEmpty(),
  body('status').isIn(['pending', 'confirmed', 'cancelled', 'completed', 'in_progress']),
  body('reason').optional().isString()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { id } = req.params;
  const { status, reason } = req.body;

  const bookingIndex = bookings.findIndex(b => b.id === id);
  if (bookingIndex === -1) {
    throw createError('Booking not found', 404);
  }

  const booking = bookings[bookingIndex];

  // Check permissions
  if (req.user?.role === 'consumer') {
    if (booking.consumerId !== req.user.id) {
      throw createError('Insufficient permissions', 403);
    }
    // Consumers can only cancel their bookings
    if (status !== 'cancelled') {
      throw createError('Consumers can only cancel bookings', 400);
    }
  } else if (req.user?.role === 'travel_agent') {
    if (booking.agentId !== req.user.id) {
      throw createError('Insufficient permissions', 403);
    }
  }
  // Super admin can update any booking

  // Update booking status
  bookings[bookingIndex] = {
    ...booking,
    status: status as BookingStatus,
    updatedAt: new Date()
  };

  res.json({
    success: true,
    data: bookings[bookingIndex],
    message: `Booking ${status} successfully`
  });
}));

// PUT /api/bookings/:id/payment - Update payment status (Travel Agent and Super Admin only)
router.put('/:id/payment', [
  requireRole(['travel_agent', 'super_admin']),
  param('id').isString().notEmpty(),
  body('paymentStatus').isIn(['pending', 'partial', 'paid', 'refunded', 'failed']),
  body('amount').optional().isFloat({ min: 0 }),
  body('transactionId').optional().isString()
], asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError('Validation failed', 400);
  }

  const { id } = req.params;
  const { paymentStatus, amount, transactionId } = req.body;

  const bookingIndex = bookings.findIndex(b => b.id === id);
  if (bookingIndex === -1) {
    throw createError('Booking not found', 404);
  }

  const booking = bookings[bookingIndex];

  // Check permissions for travel agents
  if (req.user?.role === 'travel_agent' && booking.agentId !== req.user.id) {
    throw createError('Insufficient permissions', 403);
  }

  // Update payment status
  bookings[bookingIndex] = {
    ...booking,
    paymentStatus: paymentStatus as PaymentStatus,
    updatedAt: new Date()
  };

  res.json({
    success: true,
    data: bookings[bookingIndex],
    message: 'Payment status updated successfully'
  });
}));

// GET /api/bookings/stats/overview - Get booking statistics
router.get('/stats/overview', [
  requireRole(['travel_agent', 'super_admin'])
], asyncHandler(async (req: Request, res: Response) => {
  let relevantBookings = bookings;

  // Filter by agent for travel agents
  if (req.user?.role === 'travel_agent') {
    relevantBookings = bookings.filter(booking => booking.agentId === req.user?.id);
  }

  const stats = {
    totalBookings: relevantBookings.length,
    pendingBookings: relevantBookings.filter(b => b.status === 'pending').length,
    confirmedBookings: relevantBookings.filter(b => b.status === 'confirmed').length,
    completedBookings: relevantBookings.filter(b => b.status === 'completed').length,
    cancelledBookings: relevantBookings.filter(b => b.status === 'cancelled').length,
    totalRevenue: relevantBookings
      .filter(b => b.paymentStatus === 'paid')
      .reduce((sum, b) => sum + b.totalAmount, 0),
    pendingPayments: relevantBookings
      .filter(b => b.paymentStatus === 'pending')
      .reduce((sum, b) => sum + b.totalAmount, 0),
    thisMonthBookings: relevantBookings.filter(b => {
      const bookingDate = new Date(b.bookingDate);
      const now = new Date();
      return bookingDate.getMonth() === now.getMonth() && 
             bookingDate.getFullYear() === now.getFullYear();
    }).length
  };

  res.json({
    success: true,
    data: stats
  });
}));

export default router;