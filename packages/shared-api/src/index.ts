import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';

import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';
import { authMiddleware } from './middleware/auth';

// Import routes
import routes from './routes';

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: [
    'http://localhost:3000', // React admin portal
    'http://localhost:3002', // React agent portal
    'http://localhost:8081', // React Native mobile app (Expo)
    'http://localhost:19006' // React Native web (Expo)
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compression middleware
app.use(compression());

// Logging middleware
app.use(morgan('combined'));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API routes
app.use('/api', routes);

// API documentation endpoint
app.get('/api', (req, res) => {
  res.json({
    name: 'TravelEase API',
    version: '1.0.0',
    description: 'RESTful API for TravelEase travel booking platform',
    endpoints: {
      auth: '/api/auth',
      users: '/api/users',
      packages: '/api/packages',
      bookings: '/api/bookings',
      notifications: '/api/notifications',
      analytics: '/api/analytics'
    },
    documentation: 'https://docs.travelease.com/api'
  });
});

// Error handling middleware (must be last)
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
if (process.env.NODE_ENV !== 'test') {
  app.listen(PORT, () => {
    console.log(`🚀 TravelEase API server running on port ${PORT}`);
    console.log(`📚 API documentation available at http://localhost:${PORT}/api`);
    console.log(`🏥 Health check available at http://localhost:${PORT}/health`);
  });
}

export default app;

// Export middleware and utilities for external use
export { authMiddleware, requireRole, optionalAuth } from './middleware/auth';
export { errorHandler, asyncHandler, createError } from './middleware/errorHandler';
export { notFoundHandler } from './middleware/notFoundHandler';
export { default as routes } from './routes';