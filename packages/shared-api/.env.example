# TravelEase API Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3001
HOST=localhost

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:3002

# Authentication
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined

# Features
ENABLE_MOCK_DATA=true
ENABLE_API_DOCS=true
ENABLE_CORS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
