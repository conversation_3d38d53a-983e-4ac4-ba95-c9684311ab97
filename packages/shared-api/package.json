{"name": "@travelease/shared-api", "version": "1.0.0", "description": "Shared API backend for TravelEase application", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "files": ["dist", "package.json", "README.md"], "dependencies": {"@travelease/shared-types": "workspace:*", "@travelease/mock-data": "workspace:*", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/node": "^20.10.5", "typescript": "^5.3.3", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.16"}, "publishConfig": {"access": "restricted"}}