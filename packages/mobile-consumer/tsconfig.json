{"extends": "../../tsconfig.json", "compilerOptions": {"allowJs": true, "esModuleInterop": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "node", "noEmit": true, "resolveJsonModule": true, "skipLibCheck": true, "target": "esnext", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/screens/*": ["./src/screens/*"], "@/navigation/*": ["./src/navigation/*"], "@/hooks/*": ["./src/hooks/*"], "@/store/*": ["./src/store/*"], "@/services/*": ["./src/services/*"], "@/utils/*": ["./src/utils/*"], "@/constants/*": ["./src/constants/*"], "@/types/*": ["./src/types/*"], "@/assets/*": ["./src/assets/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}