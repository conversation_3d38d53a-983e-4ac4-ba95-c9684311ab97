import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';

export function HomeScreen() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Welcome to TravelEase</Text>
        <Text style={styles.subtitle}>Discover amazing destinations</Text>
      </View>
      
      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Featured Packages</Text>
        <Text style={styles.placeholder}>Travel packages will be displayed here</Text>
        
        <Text style={styles.sectionTitle}>Recent Bookings</Text>
        <Text style={styles.placeholder}>Your recent bookings will appear here</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    padding: 20,
    backgroundColor: '#3B82F6',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#E5E7EB',
  },
  content: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
    marginTop: 20,
  },
  placeholder: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
  },
});