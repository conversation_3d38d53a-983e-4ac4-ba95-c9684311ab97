{"expo": {"name": "TravelEase", "slug": "travelease-consumer", "version": "1.0.0", "orientation": "portrait", "icon": "./src/assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./src/assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.travelease.consumer", "buildNumber": "1"}, "android": {"adaptiveIcon": {"foregroundImage": "./src/assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.travelease.consumer", "versionCode": 1}, "web": {"favicon": "./src/assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-router", "expo-font", "expo-secure-store", ["expo-notifications", {"icon": "./src/assets/notification-icon.png", "color": "#ffffff"}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow TravelEase to use your location to find nearby attractions and provide personalized recommendations."}]], "scheme": "travelease", "extra": {"router": {"origin": false}, "eas": {"projectId": "your-project-id-here"}}}}