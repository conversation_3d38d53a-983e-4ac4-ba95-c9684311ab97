# TravelEase Mobile Consumer App

A React Native mobile application built with Expo for travel consumers to browse packages, make bookings, and manage their travel experiences.

## Features

- **Authentication**: Login, signup, and password recovery
- **Home Dashboard**: Featured packages and recent bookings
- **Explore**: Browse and search travel packages
- **Bookings**: View and manage travel bookings
- **Profile**: Account management and preferences
- **Modern UI**: Glossy design with smooth animations
- **Cross-platform**: iOS and Android support

## Tech Stack

- **React Native** with Expo SDK 49
- **TypeScript** for type safety
- **React Navigation** for navigation
- **Zustand** for state management
- **React Query** for data fetching
- **Expo Linear Gradient** for beautiful gradients
- **Expo Vector Icons** for iconography
- **Expo Secure Store** for secure token storage

## Project Structure

```
src/
├── navigation/          # Navigation configuration
│   ├── RootNavigator.tsx
│   ├── AuthNavigator.tsx
│   └── MainNavigator.tsx
├── screens/            # Screen components
│   ├── auth/          # Authentication screens
│   │   ├── LoginScreen.tsx
│   │   ├── SignupScreen.tsx
│   │   └── ForgotPasswordScreen.tsx
│   ├── main/          # Main app screens
│   │   ├── HomeScreen.tsx
│   │   ├── ExploreScreen.tsx
│   │   ├── BookingsScreen.tsx
│   │   └── ProfileScreen.tsx
│   └── LoadingScreen.tsx
├── store/             # State management
│   └── authStore.ts
└── types/             # TypeScript type definitions
```

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm or yarn
- Expo CLI: `npm install -g @expo/cli`
- iOS Simulator (for iOS development)
- Android Studio (for Android development)

### Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm start
   ```

3. Run on specific platforms:
   ```bash
   npm run ios     # iOS Simulator
   npm run android # Android Emulator
   npm run web     # Web browser
   ```

### Development

- **Linting**: `npm run lint`
- **Type checking**: `npm run type-check`
- **Testing**: `npm test`

## Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
EXPO_PUBLIC_API_URL=http://localhost:3000
EXPO_PUBLIC_APP_ENV=development
```

### App Configuration

The app is configured in `app.json` with:
- App metadata (name, version, description)
- Platform-specific settings
- Required permissions
- Expo plugins and features

## State Management

The app uses Zustand for lightweight state management:

- **Auth Store**: User authentication state and actions
- **Secure Storage**: Token persistence with Expo SecureStore

## Navigation

Navigation structure:
- **Root Navigator**: Handles authentication flow
- **Auth Navigator**: Login, signup, forgot password
- **Main Navigator**: Bottom tab navigation for authenticated users

## Styling

The app uses:
- React Native StyleSheet for component styling
- Consistent color palette and typography
- Responsive design principles
- Modern glassmorphism effects

## Next Steps

1. Install Node.js and dependencies
2. Implement API integration with shared-api
3. Add real authentication flow
4. Implement package browsing and booking
5. Add push notifications
6. Implement offline support
7. Add comprehensive testing

## Contributing

This is part of the TravelEase monorepo. Follow the established patterns and conventions when adding new features.