{"name": "@travelease/mobile-consumer", "version": "1.0.0", "description": "TravelEase Consumer Mobile App - React Native with Expo", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/stack": "^6.3.20", "@tanstack/react-query": "^5.17.9", "@travelease/shared-types": "workspace:*", "@travelease/shared-ui": "workspace:*", "axios": "^1.6.5", "expo": "~50.0.0", "expo-auth-session": "~5.4.0", "expo-constants": "~15.4.5", "expo-crypto": "~12.8.1", "expo-font": "~11.10.2", "expo-image": "~1.10.1", "expo-linear-gradient": "~12.7.2", "expo-location": "~16.5.5", "expo-notifications": "~0.27.6", "expo-router": "~3.4.7", "expo-secure-store": "~12.8.1", "expo-splash-screen": "~0.26.4", "expo-status-bar": "~1.11.1", "expo-web-browser": "~12.8.2", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.48.2", "react-native": "0.73.4", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "14.1.0", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.23.7", "@expo/cli": "^0.17.0", "@types/jest": "^29.5.11", "@types/react": "~18.2.45", "@types/react-test-renderer": "^18.0.7", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "eslint": "^8.56.0", "eslint-config-expo": "^7.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-expo": "~50.0.1", "react-test-renderer": "18.2.0", "typescript": "^5.3.3"}, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}, "private": true}