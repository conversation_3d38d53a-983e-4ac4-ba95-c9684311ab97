{"name": "@travelease/shared-ui", "version": "1.0.0", "description": "Shared UI components and design system for TravelEase", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/react": "^7.6.6", "@storybook/react-vite": "^7.6.6", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.6", "typescript": "^5.3.3"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "files": ["dist"], "publishConfig": {"access": "restricted"}}