# TravelEase Design System

A comprehensive design system and component library for the TravelEase platform, built with React, TypeScript, and Tailwind CSS.

## Features

- 🎨 **Consistent Design Tokens** - Colors, typography, spacing, and more
- 🧩 **Reusable Components** - Button, Input, Card, and other UI primitives
- 🔧 **Utility Functions** - Theme helpers and CSS class utilities
- 📱 **Responsive Design** - Mobile-first approach with breakpoint utilities
- ♿ **Accessibility** - WCAG compliant components with proper ARIA attributes
- 🎯 **TypeScript Support** - Full type safety and IntelliSense

## Installation

```bash
pnpm add @travelease/shared-ui
```

## Usage

### Components

```tsx
import { Button, Input, Card } from '@travelease/shared-ui';

function MyComponent() {
  return (
    <Card>
      <Input
        label="Email"
        type="email"
        placeholder="Enter your email"
      />
      <Button variant="primary" size="lg">
        Submit
      </Button>
    </Card>
  );
}
```

### Theme Utilities

```tsx
import { getColor, getSpacing, cn } from '@travelease/shared-ui';

const customStyles = {
  backgroundColor: getColor('primary', 500),
  padding: getSpacing('md'),
};

const className = cn(
  'base-class',
  'conditional-class',
  {
    'active-class': isActive,
    'disabled-class': isDisabled
  }
);
```

### Design Tokens

```tsx
import { theme } from '@travelease/shared-ui';

// Access design tokens
const primaryColor = theme.colors.primary[500];
const headingFont = theme.typography.fontFamily.heading;
const mediumSpacing = theme.spacing.md;
```

## Components

### Button

A versatile button component with multiple variants and sizes.

**Props:**
- `variant`: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
- `size`: 'sm' | 'md' | 'lg'
- `loading`: boolean
- `leftIcon`, `rightIcon`: React.ReactNode
- `fullWidth`: boolean

### Input

A flexible input component with label, helper text, and error states.

**Props:**
- `variant`: 'default' | 'filled'
- `size`: 'sm' | 'md' | 'lg'
- `label`: string
- `helperText`: string
- `errorMessage`: string
- `leftIcon`, `rightIcon`: React.ReactNode

### Card

A container component with header, content, and footer sections.

**Props:**
- `variant`: 'default' | 'outlined' | 'elevated'
- `padding`: 'none' | 'sm' | 'md' | 'lg'
- `hoverable`: boolean

**Sub-components:**
- `CardHeader`
- `CardTitle`
- `CardDescription`
- `CardContent`
- `CardFooter`

## Design Tokens

### Colors

- **Primary**: Blue palette for main actions
- **Secondary**: Teal palette for secondary actions
- **Success**: Green palette for success states
- **Warning**: Yellow palette for warning states
- **Error**: Red palette for error states
- **Neutral**: Gray palette for text and backgrounds
- **Travel**: Themed colors for travel-specific elements

### Typography

- **Font Families**: Inter (sans), JetBrains Mono (mono)
- **Font Sizes**: xs (12px) to 4xl (36px)
- **Font Weights**: 300 to 700
- **Line Heights**: Optimized for readability

### Spacing

- **Base Scale**: 4px increments (xs: 4px, sm: 8px, md: 16px, etc.)
- **Semantic Spacing**: Component-specific spacing tokens
- **Layout Spacing**: Container and section spacing

## Utilities

### cn (Class Names)

A utility for combining CSS classes with conditional logic.

```tsx
import { cn } from '@travelease/shared-ui';

const className = cn(
  'base-class',
  condition && 'conditional-class',
  {
    'active': isActive,
    'disabled': isDisabled
  }
);
```

### Theme Helpers

- `getColor(color, shade)`: Get color values from the theme
- `getSpacing(size)`: Get spacing values from the theme
- `getTypographyStyle(style)`: Get typography styles
- `getBorderRadius(size)`: Get border radius values
- `getShadow(size)`: Get shadow values
- `responsive(breakpoint, styles)`: Create responsive styles

## Development

```bash
# Install dependencies
pnpm install

# Build the package
pnpm build

# Run Storybook (if configured)
pnpm storybook
```

## Contributing

When adding new components or modifying existing ones:

1. Follow the established patterns and conventions
2. Include proper TypeScript types
3. Add accessibility attributes where needed
4. Test with different variants and states
5. Update documentation and examples

## License

MIT License - see LICENSE file for details.