{"extends": "../../tsconfig.json", "compilerOptions": {"declaration": true, "declarationMap": true, "jsx": "react-jsx", "module": "ESNext", "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "composite": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.*", "**/*.stories.*"]}