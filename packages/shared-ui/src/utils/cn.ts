// TravelEase Design System - Class Name Utilities

import clsx, { ClassValue } from 'clsx';

/**
 * Combine class names with conditional logic
 * This is a wrapper around clsx for consistent usage across the design system
 * 
 * @param inputs - Class names, objects, or arrays to combine
 * @returns Combined class name string
 * 
 * @example
 * cn('base-class', { 'conditional-class': condition }, ['array', 'of', 'classes'])
 */
export function cn(...inputs: ClassValue[]): string {
  return clsx(inputs);
}

/**
 * Create a class name with theme-aware variants
 * 
 * @param base - Base class name
 * @param variants - Object with variant conditions
 * @param className - Additional class names to append
 * @returns Combined class name string
 * 
 * @example
 * cva('btn', {
 *   variant: {
 *     primary: 'btn-primary',
 *     secondary: 'btn-secondary'
 *   },
 *   size: {
 *     sm: 'btn-sm',
 *     lg: 'btn-lg'
 *   }
 * }, { variant: 'primary', size: 'sm' }, 'extra-class')
 */
export function cva(
  base: string,
  variants: Record<string, Record<string, string>>,
  props: Record<string, string | boolean | undefined>,
  className?: string
): string {
  const classes = [base];
  
  // Apply variant classes
  Object.entries(variants).forEach(([variantKey, variantValues]) => {
    const propValue = props[variantKey];
    if (propValue && typeof propValue === 'string' && propValue in variantValues) {
      classes.push(variantValues[propValue]);
    }
  });
  
  // Add additional className
  if (className) {
    classes.push(className);
  }
  
  return cn(classes);
}

/**
 * Create responsive class names based on breakpoint prefixes
 * 
 * @param classes - Object with breakpoint keys and class values
 * @returns Combined responsive class name string
 * 
 * @example
 * responsive({
 *   base: 'text-sm',
 *   md: 'text-base',
 *   lg: 'text-lg'
 * })
 * // Returns: 'text-sm md:text-base lg:text-lg'
 */
export function responsive(classes: Record<string, string>): string {
  const responsiveClasses: string[] = [];
  
  Object.entries(classes).forEach(([breakpoint, className]) => {
    if (breakpoint === 'base') {
      responsiveClasses.push(className);
    } else {
      responsiveClasses.push(`${breakpoint}:${className}`);
    }
  });
  
  return cn(responsiveClasses);
}

/**
 * Create state-based class names (hover, focus, active, etc.)
 * 
 * @param classes - Object with state keys and class values
 * @returns Combined state class name string
 * 
 * @example
 * states({
 *   base: 'bg-blue-500',
 *   hover: 'bg-blue-600',
 *   focus: 'ring-2 ring-blue-300',
 *   active: 'bg-blue-700'
 * })
 * // Returns: 'bg-blue-500 hover:bg-blue-600 focus:ring-2 focus:ring-blue-300 active:bg-blue-700'
 */
export function states(classes: Record<string, string>): string {
  const stateClasses: string[] = [];
  
  Object.entries(classes).forEach(([state, className]) => {
    if (state === 'base') {
      stateClasses.push(className);
    } else {
      stateClasses.push(`${state}:${className}`);
    }
  });
  
  return cn(stateClasses);
}

/**
 * Create data attribute selectors for styling
 * 
 * @param attributes - Object with data attribute keys and values
 * @returns Data attribute selector string
 * 
 * @example
 * dataAttrs({ state: 'active', variant: 'primary' })
 * // Returns: '[data-state="active"][data-variant="primary"]'
 */
export function dataAttrs(attributes: Record<string, string | boolean>): string {
  return Object.entries(attributes)
    .map(([key, value]) => {
      if (typeof value === 'boolean') {
        return value ? `[data-${key}]` : '';
      }
      return `[data-${key}="${value}"]`;
    })
    .filter(Boolean)
    .join('');
}

/**
 * Focus ring utility for consistent focus styling
 * 
 * @param variant - Focus ring variant ('default', 'primary', 'error')
 * @returns Focus ring class names
 */
export function focusRing(variant: 'default' | 'primary' | 'error' = 'default'): string {
  const variants = {
    default: 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
    primary: 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
    error: 'focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
  };
  
  return variants[variant];
}

/**
 * Screen reader only utility for accessibility
 * 
 * @returns Screen reader only class names
 */
export function srOnly(): string {
  return 'sr-only';
}

/**
 * Truncate text utility
 * 
 * @param lines - Number of lines to show (1 for single line, >1 for multi-line)
 * @returns Truncate class names
 */
export function truncate(lines: number = 1): string {
  if (lines === 1) {
    return 'truncate';
  }
  return `line-clamp-${lines}`;
}