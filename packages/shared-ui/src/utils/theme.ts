// TravelEase Design System - Theme Utilities

import { theme } from '../theme';
import type { Theme } from '../theme';

/**
 * Get a color value from the theme
 * @param colorPath - Dot notation path to the color (e.g., 'primary.500', 'text.primary')
 * @returns The color value or undefined if not found
 */
export function getColor(colorPath: string): string | undefined {
  const keys = colorPath.split('.');
  let current: any = theme.colors;
  
  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return undefined;
    }
  }
  
  return typeof current === 'string' ? current : undefined;
}

/**
 * Get a spacing value from the theme
 * @param spacingKey - The spacing key (e.g., '4', 'md', 'lg')
 * @returns The spacing value or undefined if not found
 */
export function getSpacing(spacingKey: string): string | undefined {
  if (spacingKey in theme.spacing) {
    return theme.spacing[spacingKey as keyof typeof theme.spacing];
  }
  
  // Check semantic spacing
  const semanticKeys = spacingKey.split('.');
  if (semanticKeys.length === 2) {
    const [category, size] = semanticKeys;
    if (category in theme.semanticSpacing) {
      const categorySpacing = theme.semanticSpacing[category as keyof typeof theme.semanticSpacing];
      if (size in categorySpacing) {
        return categorySpacing[size as keyof typeof categorySpacing];
      }
    }
  }
  
  return undefined;
}

/**
 * Get a typography style from the theme
 * @param styleKey - The typography style key (e.g., 'h1', 'body', 'button')
 * @returns The typography style object or undefined if not found
 */
export function getTypographyStyle(styleKey: string): Record<string, string> | undefined {
  if (styleKey in theme.typography.textStyles) {
    return theme.typography.textStyles[styleKey as keyof typeof theme.typography.textStyles];
  }
  return undefined;
}

/**
 * Get a border radius value from the theme
 * @param radiusKey - The border radius key (e.g., 'sm', 'md', 'lg')
 * @returns The border radius value or undefined if not found
 */
export function getBorderRadius(radiusKey: string): string | undefined {
  if (radiusKey in theme.borderRadius) {
    return theme.borderRadius[radiusKey as keyof typeof theme.borderRadius];
  }
  return undefined;
}

/**
 * Get a shadow value from the theme
 * @param shadowKey - The shadow key (e.g., 'sm', 'md', 'lg')
 * @returns The shadow value or undefined if not found
 */
export function getShadow(shadowKey: string): string | undefined {
  if (shadowKey in theme.shadows) {
    return theme.shadows[shadowKey as keyof typeof theme.shadows];
  }
  return undefined;
}

/**
 * Create a CSS-in-JS style object from theme values
 * @param styles - Object with theme-aware style properties
 * @returns CSS-in-JS style object
 */
export function createStyles(styles: Record<string, any>): Record<string, any> {
  const result: Record<string, any> = {};
  
  for (const [property, value] of Object.entries(styles)) {
    if (typeof value === 'string') {
      // Handle theme references
      if (value.startsWith('$color.')) {
        result[property] = getColor(value.slice(7));
      } else if (value.startsWith('$spacing.')) {
        result[property] = getSpacing(value.slice(9));
      } else if (value.startsWith('$radius.')) {
        result[property] = getBorderRadius(value.slice(8));
      } else if (value.startsWith('$shadow.')) {
        result[property] = getShadow(value.slice(8));
      } else {
        result[property] = value;
      }
    } else {
      result[property] = value;
    }
  }
  
  return result;
}

/**
 * Generate responsive styles based on breakpoints
 * @param styles - Object with breakpoint keys and style values
 * @returns CSS media query object
 */
export function responsive(styles: Record<string, Record<string, any>>): Record<string, any> {
  const result: Record<string, any> = {};
  
  for (const [breakpoint, breakpointStyles] of Object.entries(styles)) {
    if (breakpoint in theme.breakpoints) {
      const minWidth = theme.breakpoints[breakpoint as keyof typeof theme.breakpoints];
      if (minWidth === '0px') {
        // Base styles (no media query)
        Object.assign(result, createStyles(breakpointStyles));
      } else {
        result[`@media (min-width: ${minWidth})`] = createStyles(breakpointStyles);
      }
    }
  }
  
  return result;
}

/**
 * Create a variant style based on component configuration
 * @param component - Component name (e.g., 'button', 'input')
 * @param variant - Variant name (e.g., 'primary', 'secondary')
 * @param size - Size name (e.g., 'sm', 'md', 'lg')
 * @returns Combined style object
 */
export function getComponentVariant(
  component: string,
  variant?: string,
  size?: string
): Record<string, any> {
  const componentConfig = theme.components[component as keyof typeof theme.components];
  if (!componentConfig) return {};
  
  let styles: Record<string, any> = {};
  
  // Apply size styles
  if (size && 'sizes' in componentConfig) {
    const sizeConfig = (componentConfig as any).sizes;
    if (size in sizeConfig) {
      styles = { ...styles, ...sizeConfig[size] };
    }
  }
  
  // Apply variant styles
  if (variant && 'variants' in componentConfig) {
    const variantConfig = (componentConfig as any).variants;
    if (variant in variantConfig) {
      styles = { ...styles, ...variantConfig[variant] };
    }
  }
  
  return styles;
}

/**
 * Convert theme values to CSS custom properties
 * @returns CSS custom properties object
 */
export function createCSSVariables(): Record<string, string> {
  const variables: Record<string, string> = {};
  
  // Colors
  Object.entries(theme.colors).forEach(([colorName, colorValue]) => {
    if (typeof colorValue === 'object') {
      Object.entries(colorValue).forEach(([shade, value]) => {
        variables[`--color-${colorName}-${shade}`] = value;
      });
    } else {
      variables[`--color-${colorName}`] = colorValue;
    }
  });
  
  // Spacing
  Object.entries(theme.spacing).forEach(([key, value]) => {
    variables[`--spacing-${key}`] = value;
  });
  
  // Typography
  Object.entries(theme.typography.fontSize).forEach(([key, value]) => {
    variables[`--font-size-${key}`] = value;
  });
  
  // Border radius
  Object.entries(theme.borderRadius).forEach(([key, value]) => {
    variables[`--radius-${key}`] = value;
  });
  
  // Shadows
  Object.entries(theme.shadows).forEach(([key, value]) => {
    variables[`--shadow-${key}`] = value;
  });
  
  return variables;
}