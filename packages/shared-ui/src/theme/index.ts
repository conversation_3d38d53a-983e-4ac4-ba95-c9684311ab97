// TravelEase Design System - Main Theme Configuration

import { colors } from './colors';
import { typography } from './typography';
import { spacing, semanticSpacing, borderRadius, shadows, zIndex, breakpoints } from './spacing';

export const theme = {
  colors,
  typography,
  spacing,
  semanticSpacing,
  borderRadius,
  shadows,
  zIndex,
  breakpoints,

  // Animation and transitions
  animation: {
    duration: {
      fast: '150ms',
      normal: '200ms',
      slow: '300ms',
      slower: '500ms'
    },
    easing: {
      linear: 'linear',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
  },

  // Component variants
  components: {
    button: {
      sizes: {
        sm: {
          height: '2rem',
          padding: '0 0.75rem',
          fontSize: typography.fontSize.sm
        },
        md: {
          height: '2.5rem',
          padding: '0 1rem',
          fontSize: typography.fontSize.sm
        },
        lg: {
          height: '3rem',
          padding: '0 1.5rem',
          fontSize: typography.fontSize.base
        }
      },
      variants: {
        primary: {
          backgroundColor: colors.primary[500],
          color: colors.text.inverse,
          borderColor: colors.primary[500]
        },
        secondary: {
          backgroundColor: colors.neutral[100],
          color: colors.text.primary,
          borderColor: colors.border.primary
        },
        outline: {
          backgroundColor: 'transparent',
          color: colors.primary[500],
          borderColor: colors.primary[500]
        },
        ghost: {
          backgroundColor: 'transparent',
          color: colors.text.primary,
          borderColor: 'transparent'
        },
        danger: {
          backgroundColor: colors.error[500],
          color: colors.text.inverse,
          borderColor: colors.error[500]
        }
      }
    },

    input: {
      sizes: {
        sm: {
          height: '2rem',
          padding: '0 0.75rem',
          fontSize: typography.fontSize.sm
        },
        md: {
          height: '2.5rem',
          padding: '0 0.75rem',
          fontSize: typography.fontSize.sm
        },
        lg: {
          height: '3rem',
          padding: '0 1rem',
          fontSize: typography.fontSize.base
        }
      },
      variants: {
        default: {
          backgroundColor: colors.background.primary,
          borderColor: colors.border.primary,
          color: colors.text.primary
        },
        filled: {
          backgroundColor: colors.background.secondary,
          borderColor: 'transparent',
          color: colors.text.primary
        }
      }
    },

    card: {
      variants: {
        default: {
          backgroundColor: colors.background.primary,
          borderColor: colors.border.primary,
          borderRadius: borderRadius.lg,
          boxShadow: shadows.sm
        },
        elevated: {
          backgroundColor: colors.background.primary,
          borderColor: 'transparent',
          borderRadius: borderRadius.lg,
          boxShadow: shadows.md
        },
        outlined: {
          backgroundColor: colors.background.primary,
          borderColor: colors.border.primary,
          borderRadius: borderRadius.lg,
          boxShadow: shadows.none
        }
      }
    }
  }
} as const;

// Export individual theme parts
export { colors } from './colors';
export { typography } from './typography';
export { spacing, semanticSpacing, borderRadius, shadows, zIndex, breakpoints } from './spacing';

// Export types
export type Theme = typeof theme;
export type ThemeColors = typeof colors;
export type ThemeTypography = typeof typography;
export type ThemeSpacing = typeof spacing;

// Default export
export default theme;