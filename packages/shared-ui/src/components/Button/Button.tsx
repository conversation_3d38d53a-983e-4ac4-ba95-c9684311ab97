import React, { forwardRef } from 'react';
import { cn } from '../../utils/cn';
import { getComponentVariant } from '../../utils/theme';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** Button variant */
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  /** Button size */
  size?: 'sm' | 'md' | 'lg';
  /** Whether the button is in a loading state */
  loading?: boolean;
  /** Whether the button is disabled */
  disabled?: boolean;
  /** Icon to display before the button text */
  leftIcon?: React.ReactNode;
  /** Icon to display after the button text */
  rightIcon?: React.ReactNode;
  /** Whether the button should take full width */
  fullWidth?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Button content */
  children?: React.ReactNode;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>((
  {
    variant = 'primary',
    size = 'md',
    loading = false,
    disabled = false,
    leftIcon,
    rightIcon,
    fullWidth = false,
    className,
    children,
    type = 'button',
    ...props
  },
  ref: React.Ref<HTMLButtonElement>
) => {
  const baseClasses = [
    // Base styles
    'inline-flex',
    'items-center',
    'justify-center',
    'gap-2',
    'font-medium',
    'rounded-lg',
    'border',
    'transition-all',
    'duration-200',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-offset-2',
    'disabled:opacity-50',
    'disabled:cursor-not-allowed',
    'disabled:pointer-events-none'
  ];

  // Size-specific classes
  const sizeClasses = {
    sm: ['h-8', 'px-3', 'text-sm'],
    md: ['h-10', 'px-4', 'text-sm'],
    lg: ['h-12', 'px-6', 'text-base']
  };

  // Variant-specific classes
  const variantClasses = {
    primary: [
      'bg-blue-600',
      'text-white',
      'border-blue-600',
      'hover:bg-blue-700',
      'hover:border-blue-700',
      'focus:ring-blue-500',
      'active:bg-blue-800'
    ],
    secondary: [
      'bg-gray-100',
      'text-gray-900',
      'border-gray-300',
      'hover:bg-gray-200',
      'hover:border-gray-400',
      'focus:ring-gray-500',
      'active:bg-gray-300'
    ],
    outline: [
      'bg-transparent',
      'text-blue-600',
      'border-blue-600',
      'hover:bg-blue-50',
      'hover:text-blue-700',
      'focus:ring-blue-500',
      'active:bg-blue-100'
    ],
    ghost: [
      'bg-transparent',
      'text-gray-700',
      'border-transparent',
      'hover:bg-gray-100',
      'hover:text-gray-900',
      'focus:ring-gray-500',
      'active:bg-gray-200'
    ],
    danger: [
      'bg-red-600',
      'text-white',
      'border-red-600',
      'hover:bg-red-700',
      'hover:border-red-700',
      'focus:ring-red-500',
      'active:bg-red-800'
    ]
  };

  // Full width classes
  const widthClasses = fullWidth ? ['w-full'] : [];

  // Loading state classes
  const loadingClasses = loading ? ['cursor-wait'] : [];

  const buttonClasses = cn(
    baseClasses,
    sizeClasses[size as keyof typeof sizeClasses],
    variantClasses[variant as keyof typeof variantClasses],
    widthClasses,
    loadingClasses,
    className
  );

  const isDisabled = disabled || loading;

  return (
    <button
      ref={ref}
      type={type}
      disabled={isDisabled}
      className={buttonClasses}
      {...props}
    >
      {loading && (
        <svg
          className="animate-spin h-4 w-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {!loading && leftIcon && (
        <span className="flex-shrink-0">{leftIcon}</span>
      )}
      {children && (
        <span className={cn(loading && 'opacity-0')}>{children}</span>
      )}
      {!loading && rightIcon && (
        <span className="flex-shrink-0">{rightIcon}</span>
      )}
    </button>
  );
});

Button.displayName = 'Button';

export { Button };
export default Button;