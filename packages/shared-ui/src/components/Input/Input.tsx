import React, { forwardRef } from 'react';
import { cn } from '../../utils/cn';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  /** Input variant */
  variant?: 'default' | 'filled';
  /** Input size */
  size?: 'sm' | 'md' | 'lg';
  /** Whether the input has an error state */
  error?: boolean;
  /** Icon to display before the input */
  leftIcon?: React.ReactNode;
  /** Icon to display after the input */
  rightIcon?: React.ReactNode;
  /** Label for the input */
  label?: string;
  /** Helper text to display below the input */
  helperText?: string;
  /** Error message to display below the input */
  errorMessage?: string;
  /** Whether the input is required */
  required?: boolean;
  /** Additional CSS classes */
  className?: string;
}

const Input = forwardRef<HTMLInputElement, InputProps>((
  {
    variant = 'default',
    size = 'md',
    error = false,
    leftIcon,
    rightIcon,
    label,
    helperText,
    errorMessage,
    required = false,
    className,
    id,
    disabled,
    ...props
  },
  ref: React.Ref<HTMLInputElement>
) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
  const hasError = error || !!errorMessage;
  const hasIcons = leftIcon || rightIcon;

  // Base input classes
  const baseClasses = [
    'w-full',
    'border',
    'rounded-lg',
    'transition-all',
    'duration-200',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-offset-1',
    'disabled:opacity-50',
    'disabled:cursor-not-allowed',
    'disabled:bg-gray-50'
  ];

  // Size-specific classes
  const sizeClasses = {
    sm: hasIcons ? ['h-8', 'px-8', 'text-sm'] : ['h-8', 'px-3', 'text-sm'],
    md: hasIcons ? ['h-10', 'px-10', 'text-sm'] : ['h-10', 'px-3', 'text-sm'],
    lg: hasIcons ? ['h-12', 'px-12', 'text-base'] : ['h-12', 'px-4', 'text-base']
  };

  // Variant-specific classes
  const variantClasses = {
    default: [
      'bg-white',
      'border-gray-300',
      'text-gray-900',
      'placeholder-gray-500',
      'focus:border-blue-500',
      'focus:ring-blue-500'
    ],
    filled: [
      'bg-gray-50',
      'border-transparent',
      'text-gray-900',
      'placeholder-gray-500',
      'focus:bg-white',
      'focus:border-blue-500',
      'focus:ring-blue-500'
    ]
  };

  // Error state classes
  const errorClasses = hasError ? [
    'border-red-500',
    'focus:border-red-500',
    'focus:ring-red-500'
  ] : [];

  const inputClasses = cn(
    baseClasses,
    sizeClasses[size as keyof typeof sizeClasses],
    variantClasses[variant as keyof typeof variantClasses],
    errorClasses,
    className
  );

  // Icon positioning classes
  const iconSize = {
    sm: 'w-4 h-4',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  const leftIconClasses = cn(
    'absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none',
    iconSize[size as keyof typeof iconSize]
  );

  const rightIconClasses = cn(
    'absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none',
    iconSize[size as keyof typeof iconSize]
  );

  return (
    <div className="w-full">
      {label && (
        <label
          htmlFor={inputId}
          className={cn(
            'block text-sm font-medium text-gray-700 mb-1',
            required && 'after:content-["*"] after:text-red-500 after:ml-1'
          )}
        >
          {label}
        </label>
      )}
      
      <div className="relative">
        {leftIcon && (
          <div className={leftIconClasses}>
            {leftIcon}
          </div>
        )}
        
        <input
          ref={ref}
          id={inputId}
          disabled={disabled}
          className={inputClasses}
          {...props}
        />
        
        {rightIcon && (
          <div className={rightIconClasses}>
            {rightIcon}
          </div>
        )}
      </div>
      
      {(helperText || errorMessage) && (
        <div className="mt-1">
          {hasError && errorMessage ? (
            <p className="text-sm text-red-600">{errorMessage}</p>
          ) : helperText ? (
            <p className="text-sm text-gray-500">{helperText}</p>
          ) : null}
        </div>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export { Input };
export default Input;