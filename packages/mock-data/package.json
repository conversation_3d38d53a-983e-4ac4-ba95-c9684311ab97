{"name": "@travelease/mock-data", "version": "1.0.0", "description": "Mock data generators for TravelEase application", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "generate": "node dist/generate.js"}, "files": ["dist", "src", "data"], "dependencies": {"@travelease/shared-types": "workspace:*"}, "devDependencies": {"typescript": "^5.1.6", "@types/node": "^20.4.0"}, "publishConfig": {"access": "restricted"}}