# @travelease/mock-data

Mock data generation package for the TravelEase application. Provides realistic sample data for users, travel packages, bookings, notifications, and analytics.

## Features

- **MockDataGenerator**: Class with static methods to generate individual mock data items
- **Pre-generated Data**: Ready-to-use collections of mock data
- **Utility Functions**: Helper functions to query and filter mock data
- **TypeScript Support**: Full type safety with shared types from `@travelease/shared-types`

## Usage

### Using Pre-generated Data

```typescript
import { mockData, users, travelPackages, bookings } from '@travelease/mock-data';

// Access all data
console.log(mockData.users.all); // All users
console.log(mockData.users.travelAgents); // Only travel agents
console.log(mockData.travelPackages); // All travel packages

// Use individual collections
const allUsers = users;
const allPackages = travelPackages;
const allBookings = bookings;
```

### Using Utility Functions

```typescript
import { getMockData } from '@travelease/mock-data';

// Get specific data
const user = getMockData.getUserById('user123');
const agentPackages = getMockData.getPackagesByAgent('agent456');
const userBookings = getMockData.getBookingsByConsumer('consumer789');
const unreadNotifications = getMockData.getUnreadNotifications('user123');
```

### Generating Custom Data

```typescript
import { MockDataGenerator } from '@travelease/mock-data';

// Generate individual items
const newUser = MockDataGenerator.generateConsumer();
const newPackage = MockDataGenerator.generateTravelPackage('agent123');
const newBooking = MockDataGenerator.generateBooking('package456', 'consumer789');

// Generate collections
const users = MockDataGenerator.generateUsers(50);
const packages = MockDataGenerator.generateTravelPackages(20, agentIds);
const bookings = MockDataGenerator.generateBookings(100, packageIds, consumerIds);
```

## Data Structure

The pre-generated mock data includes:

- **100 Users**: 1 Super Admin, ~20 Travel Agents, ~79 Consumers
- **50 Travel Packages**: Distributed across different agents and categories
- **200 Bookings**: Various statuses and payment states
- **150 Notifications**: Different types and priorities
- **Analytics Data**: Dashboard metrics and statistics

## Available Generators

### User Generators
- `generateSuperAdmin()`
- `generateTravelAgent()`
- `generateConsumer()`
- `generateUsers(count)`

### Travel Package Generators
- `generateTravelPackage(agentId?)`
- `generateTravelPackages(count, agentIds?)`
- `generateItinerary(duration)`
- `generateActivities(count)`

### Booking Generators
- `generateBooking(packageId?, consumerId?, agentId?)`
- `generateBookings(count, packageIds?, consumerIds?, agentIds?)`
- `generateTravelers(count)`

### Other Generators
- `generateLocation()`
- `generateAccommodation()`
- `generateTransportation()`
- `generateNotification(userId?)`
- `generateAnalyticsData()`

## Dependencies

- `@travelease/shared-types`: TypeScript interfaces and types
- `typescript`: Development dependency for building

## Build

```bash
# Build the package
npm run build

# Watch for changes
npm run dev

# Type check
npm run type-check
```