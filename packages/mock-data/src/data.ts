import MockDataGenerator from './index';
import { User, TravelPackage, Booking, Notification, AnalyticsData } from '@travelease/shared-types';

// Generate mock data sets
const users = MockDataGenerator.generateUsers(100);
const superAdmins = users.filter(user => user.role === 'super_admin');
const travelAgents = users.filter(user => user.role === 'travel_agent');
const consumers = users.filter(user => user.role === 'consumer');

const agentIds = travelAgents.map(agent => agent.id);
const consumerIds = consumers.map(consumer => consumer.id);

const travelPackages = MockDataGenerator.generateTravelPackages(50, agentIds);
const packageIds = travelPackages.map(pkg => pkg.id);

const bookings = MockDataGenerator.generateBookings(200, packageIds, consumerIds, agentIds);
const notifications = MockDataGenerator.generateNotifications(150, users.map(u => u.id));
const analytics = MockDataGenerator.generateAnalyticsData();

// Export organized mock data
export const mockData = {
  users: {
    all: users,
    superAdmins,
    travelAgents,
    consumers
  },
  travelPackages,
  bookings,
  notifications,
  analytics
};

// Export individual collections
export { users, travelPackages, bookings, notifications, analytics };

// Export specific user types
export { superAdmins, travelAgents, consumers };

// Export utility functions for getting specific data
export const getMockData = {
  // User functions
  getUserById: (id: string) => users.find(user => user.id === id),
  getUsersByRole: (role: string) => users.filter(user => user.role === role),
  getRandomUser: () => users[Math.floor(Math.random() * users.length)],
  
  // Package functions
  getPackageById: (id: string) => travelPackages.find(pkg => pkg.id === id),
  getPackagesByAgent: (agentId: string) => travelPackages.filter(pkg => pkg.agentId === agentId),
  getPackagesByCategory: (category: string) => travelPackages.filter(pkg => pkg.category === category),
  getRandomPackage: () => travelPackages[Math.floor(Math.random() * travelPackages.length)],
  
  // Booking functions
  getBookingById: (id: string) => bookings.find(booking => booking.id === id),
  getBookingsByConsumer: (consumerId: string) => bookings.filter(booking => booking.consumerId === consumerId),
  getBookingsByAgent: (agentId: string) => bookings.filter(booking => booking.agentId === agentId),
  getBookingsByStatus: (status: string) => bookings.filter(booking => booking.status === status),
  getRandomBooking: () => bookings[Math.floor(Math.random() * bookings.length)],
  
  // Notification functions
  getNotificationById: (id: string) => notifications.find(notification => notification.id === id),
  getNotificationsByUser: (userId: string) => notifications.filter(notification => notification.userId === userId),
  getUnreadNotifications: (userId: string) => notifications.filter(notification => notification.userId === userId && !notification.isRead),
  getRandomNotification: () => notifications[Math.floor(Math.random() * notifications.length)]
};

// Default export
export default mockData;