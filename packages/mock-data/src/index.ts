import {
  User,
  TravelAgent,
  Consumer,
  SuperAdmin,
  TravelPackage,
  Booking,
  ItineraryItem,
  Activity,
  Location,
  Accommodation,
  Transportation,
  Notification,
  UserRole,
  BookingStatus,
  PaymentStatus,
  PackageCategory,
  ActivityType,
  AccommodationType,
  TransportationType,
  NotificationType,
  Traveler,
  PricingTier,
  AnalyticsData,
  DestinationStats,
  MonthlyStats
} from '@travelease/shared-types';

// Utility functions for generating random data
class MockDataGenerator {
  private static getRandomElement<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }

  private static getRandomNumber(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  private static getRandomDate(start: Date, end: Date): Date {
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  }

  private static generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private static getRandomBoolean(): boolean {
    return Math.random() > 0.5;
  }

  // Sample data arrays
  private static firstNames = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
  ];

  private static last<PERSON><PERSON> = [
    '<PERSON>', '<PERSON>', '<PERSON>', 'Brown', 'Jones', 'Garcia', 'Miller',
    'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez',
    'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin',
    'Lee', 'Perez', 'Thompson', 'White', 'Harris', 'Sanchez', 'Clark',
    'Ramirez', 'Lewis', 'Robinson'
  ];

  private static destinations = [
    'Paris, France', 'Tokyo, Japan', 'New York, USA', 'London, UK',
    'Rome, Italy', 'Barcelona, Spain', 'Amsterdam, Netherlands',
    'Sydney, Australia', 'Dubai, UAE', 'Bangkok, Thailand',
    'Istanbul, Turkey', 'Prague, Czech Republic', 'Vienna, Austria',
    'Bali, Indonesia', 'Santorini, Greece', 'Machu Picchu, Peru',
    'Cairo, Egypt', 'Marrakech, Morocco', 'Kyoto, Japan', 'Venice, Italy'
  ];

  private static packageTitles = [
    'Romantic Paris Getaway', 'Tokyo Adventure Tour', 'New York City Explorer',
    'London Historical Journey', 'Roman Empire Discovery', 'Barcelona Art & Culture',
    'Amsterdam Canal Cruise', 'Sydney Harbor Experience', 'Dubai Luxury Escape',
    'Bangkok Street Food Tour', 'Istanbul Cultural Heritage', 'Prague Castle Tour',
    'Vienna Classical Music', 'Bali Beach Paradise', 'Santorini Sunset Tour',
    'Machu Picchu Trek', 'Egyptian Pyramids Expedition', 'Moroccan Desert Safari',
    'Kyoto Temple Walk', 'Venetian Gondola Romance'
  ];

  private static agencyNames = [
    'Global Adventures', 'Dream Destinations', 'Wanderlust Travel',
    'Elite Expeditions', 'Sunset Tours', 'Adventure Seekers',
    'Cultural Journeys', 'Luxury Escapes', 'Budget Backpackers',
    'Family Fun Travel', 'Romantic Getaways', 'Business Travel Pro',
    'Eco Adventures', 'City Breaks', 'Mountain Expeditions'
  ];

  private static activities = [
    'City Walking Tour', 'Museum Visit', 'Local Cuisine Tasting',
    'Historical Site Tour', 'Shopping Experience', 'Cultural Show',
    'Adventure Sports', 'Beach Activities', 'Mountain Hiking',
    'Boat Cruise', 'Photography Tour', 'Art Gallery Visit',
    'Local Market Exploration', 'Cooking Class', 'Wine Tasting',
    'Spa Treatment', 'Wildlife Safari', 'Scenic Drive', 'Temple Visit',
    'Traditional Dance Show'
  ];

  // User generators
  static generateSuperAdmin(): SuperAdmin {
    const id = this.generateId();
    const firstName = this.getRandomElement(this.firstNames);
    const lastName = this.getRandomElement(this.lastNames);
    
    return {
      id,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@travelease.com`,
      firstName,
      lastName,
      role: 'super_admin',
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${id}`,
      phone: `+1${this.getRandomNumber(**********, **********)}`,
      createdAt: this.getRandomDate(new Date(2023, 0, 1), new Date()),
      updatedAt: new Date(),
      isActive: true,
      permissions: ['manage_agents', 'view_analytics', 'system_config', 'audit_logs']
    };
  }

  static generateTravelAgent(): TravelAgent {
    const id = this.generateId();
    const firstName = this.getRandomElement(this.firstNames);
    const lastName = this.getRandomElement(this.lastNames);
    
    return {
      id,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@agent.com`,
      firstName,
      lastName,
      role: 'travel_agent',
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${id}`,
      phone: `+1${this.getRandomNumber(**********, **********)}`,
      createdAt: this.getRandomDate(new Date(2023, 0, 1), new Date()),
      updatedAt: new Date(),
      isActive: this.getRandomBoolean(),
      agencyName: this.getRandomElement(this.agencyNames),
      licenseNumber: `LIC${this.getRandomNumber(100000, 999999)}`,
      verificationStatus: this.getRandomElement(['pending', 'verified', 'rejected']),
      specializations: [
        this.getRandomElement(['adventure', 'cultural', 'luxury', 'budget', 'family']),
        this.getRandomElement(['domestic', 'international', 'business', 'leisure'])
      ],
      rating: Math.round((Math.random() * 2 + 3) * 10) / 10, // 3.0 to 5.0
      totalBookings: this.getRandomNumber(0, 500)
    };
  }

  static generateConsumer(): Consumer {
    const id = this.generateId();
    const firstName = this.getRandomElement(this.firstNames);
    const lastName = this.getRandomElement(this.lastNames);
    
    return {
      id,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@email.com`,
      firstName,
      lastName,
      role: 'consumer',
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${id}`,
      phone: `+1${this.getRandomNumber(**********, **********)}`,
      createdAt: this.getRandomDate(new Date(2023, 0, 1), new Date()),
      updatedAt: new Date(),
      isActive: true,
      preferences: {
        budgetRange: {
          min: this.getRandomNumber(500, 2000),
          max: this.getRandomNumber(3000, 10000),
          currency: 'USD'
        },
        preferredDestinations: [
          this.getRandomElement(this.destinations),
          this.getRandomElement(this.destinations)
        ],
        travelStyle: this.getRandomElement(['budget', 'mid_range', 'luxury']),
        groupSize: this.getRandomElement(['solo', 'couple', 'family', 'group']),
        interests: [
          this.getRandomElement(['culture', 'adventure', 'food', 'history', 'nature']),
          this.getRandomElement(['photography', 'shopping', 'nightlife', 'relaxation'])
        ],
        dietaryRestrictions: this.getRandomBoolean() ? ['vegetarian'] : [],
        accessibilityNeeds: []
      },
      emergencyContact: {
        name: `${this.getRandomElement(this.firstNames)} ${this.getRandomElement(this.lastNames)}`,
        relationship: this.getRandomElement(['spouse', 'parent', 'sibling', 'friend']),
        phone: `+1${this.getRandomNumber(**********, **********)}`,
        email: `emergency${this.getRandomNumber(1, 1000)}@email.com`
      }
    };
  }

  // Location generator
  static generateLocation(): Location {
    const cities = [
      { name: 'Paris', country: 'France', lat: 48.8566, lng: 2.3522 },
      { name: 'Tokyo', country: 'Japan', lat: 35.6762, lng: 139.6503 },
      { name: 'New York', country: 'USA', lat: 40.7128, lng: -74.0060 },
      { name: 'London', country: 'UK', lat: 51.5074, lng: -0.1278 },
      { name: 'Rome', country: 'Italy', lat: 41.9028, lng: 12.4964 }
    ];
    
    const city = this.getRandomElement(cities);
    
    return {
      id: this.generateId(),
      name: city.name,
      address: `${this.getRandomNumber(1, 999)} Main Street`,
      city: city.name,
      country: city.country,
      coordinates: {
        latitude: city.lat + (Math.random() - 0.5) * 0.1,
        longitude: city.lng + (Math.random() - 0.5) * 0.1
      },
      timezone: 'UTC+0'
    };
  }

  // Travel Package generator
  static generateTravelPackage(agentId?: string): TravelPackage {
    const id = this.generateId();
    const title = this.getRandomElement(this.packageTitles);
    const destination = this.getRandomElement(this.destinations);
    const duration = this.getRandomNumber(3, 14);
    
    return {
      id,
      title,
      description: `Experience the best of ${destination} with our carefully curated ${duration}-day tour. This package includes guided tours, comfortable accommodations, and authentic local experiences.`,
      destination,
      duration,
      maxGroupSize: this.getRandomNumber(8, 20),
      minAge: this.getRandomNumber(12, 18),
      difficulty: this.getRandomElement(['easy', 'moderate', 'challenging']),
      category: this.getRandomElement(['adventure', 'cultural', 'relaxation', 'business', 'family', 'luxury']),
      pricing: this.generatePricingTiers(),
      itinerary: this.generateItinerary(duration),
      inclusions: [
        'Accommodation',
        'Daily breakfast',
        'Professional guide',
        'Transportation',
        'Entrance fees'
      ],
      exclusions: [
        'International flights',
        'Travel insurance',
        'Personal expenses',
        'Lunch and dinner (unless specified)'
      ],
      images: [
        `https://picsum.photos/800/600?random=${this.getRandomNumber(1, 1000)}`,
        `https://picsum.photos/800/600?random=${this.getRandomNumber(1, 1000)}`,
        `https://picsum.photos/800/600?random=${this.getRandomNumber(1, 1000)}`
      ],
      agentId: agentId || this.generateId(),
      status: this.getRandomElement(['draft', 'published', 'archived']),
      createdAt: this.getRandomDate(new Date(2023, 0, 1), new Date()),
      updatedAt: new Date()
    };
  }

  static generatePricingTiers(): PricingTier[] {
    return [
      {
        id: this.generateId(),
        name: 'Standard',
        description: 'Basic package with essential services',
        basePrice: this.getRandomNumber(800, 1500),
        currency: 'USD',
        groupDiscounts: [
          { minSize: 4, discountPercentage: 5 },
          { minSize: 8, discountPercentage: 10 }
        ]
      },
      {
        id: this.generateId(),
        name: 'Premium',
        description: 'Enhanced package with additional amenities',
        basePrice: this.getRandomNumber(1500, 2500),
        currency: 'USD',
        groupDiscounts: [
          { minSize: 4, discountPercentage: 5 },
          { minSize: 8, discountPercentage: 10 }
        ]
      }
    ];
  }

  static generateItinerary(duration: number): ItineraryItem[] {
    const itinerary: ItineraryItem[] = [];
    
    for (let day = 1; day <= duration; day++) {
      itinerary.push({
        id: this.generateId(),
        day,
        title: `Day ${day}: ${this.getRandomElement(['Exploration', 'Adventure', 'Cultural', 'Relaxation', 'Discovery'])}`,
        description: `Exciting activities and experiences planned for day ${day} of your journey.`,
        activities: this.generateActivities(this.getRandomNumber(2, 4)),
        accommodation: day < duration ? this.generateAccommodation() : undefined,
        meals: this.getRandomElement([['breakfast'], ['breakfast', 'lunch'], ['breakfast', 'dinner'], ['breakfast', 'lunch', 'dinner']]),
        transportation: day > 1 ? this.generateTransportation() : undefined,
        location: this.generateLocation()
      });
    }
    
    return itinerary;
  }

  static generateActivities(count: number): Activity[] {
    const activities: Activity[] = [];
    
    for (let i = 0; i < count; i++) {
      activities.push({
        id: this.generateId(),
        name: this.getRandomElement(this.activities),
        description: 'An exciting activity that will enhance your travel experience.',
        duration: this.getRandomNumber(60, 240), // 1-4 hours
        type: this.getRandomElement(['sightseeing', 'adventure', 'cultural', 'dining', 'shopping', 'relaxation']),
        cost: this.getRandomBoolean() ? this.getRandomNumber(20, 100) : undefined,
        isOptional: this.getRandomBoolean(),
        requirements: this.getRandomBoolean() ? ['Comfortable walking shoes'] : undefined
      });
    }
    
    return activities;
  }

  static generateAccommodation(): Accommodation {
    const hotelNames = [
      'Grand Palace Hotel', 'Sunset Resort', 'City Center Inn', 'Mountain View Lodge',
      'Seaside Villa', 'Urban Boutique Hotel', 'Heritage Manor', 'Modern Suites'
    ];
    
    return {
      id: this.generateId(),
      name: this.getRandomElement(hotelNames),
      type: this.getRandomElement(['hotel', 'resort', 'hostel', 'apartment', 'villa', 'camping']),
      rating: this.getRandomNumber(3, 5),
      location: this.generateLocation(),
      amenities: [
        'Free WiFi',
        'Swimming Pool',
        'Fitness Center',
        'Restaurant',
        'Room Service'
      ],
      checkIn: '15:00',
      checkOut: '11:00',
      images: [
        `https://picsum.photos/600/400?random=${this.getRandomNumber(1, 1000)}`,
        `https://picsum.photos/600/400?random=${this.getRandomNumber(1, 1000)}`
      ]
    };
  }

  static generateTransportation(): Transportation {
    return {
      id: this.generateId(),
      type: this.getRandomElement(['flight', 'train', 'bus', 'car', 'boat', 'walking']),
      provider: this.getRandomElement(['Airlines Inc', 'Rail Express', 'Bus Lines', 'Car Rental', 'Ferry Co']),
      departureTime: `${this.getRandomNumber(6, 22)}:${this.getRandomNumber(0, 5)}0`,
      arrivalTime: `${this.getRandomNumber(8, 23)}:${this.getRandomNumber(0, 5)}0`,
      from: this.generateLocation(),
      to: this.generateLocation(),
      seatClass: this.getRandomElement(['Economy', 'Business', 'First Class']),
      ticketNumber: `TK${this.getRandomNumber(100000, 999999)}`
    };
  }

  // Booking generator
  static generateBooking(packageId?: string, consumerId?: string, agentId?: string): Booking {
    const id = this.generateId();
    const travelDate = this.getRandomDate(new Date(), new Date(2024, 11, 31));
    
    return {
      id,
      packageId: packageId || this.generateId(),
      consumerId: consumerId || this.generateId(),
      agentId: agentId || this.generateId(),
      status: this.getRandomElement(['pending', 'confirmed', 'cancelled', 'completed', 'in_progress']),
      travelers: this.generateTravelers(this.getRandomNumber(1, 4)),
      totalAmount: this.getRandomNumber(1000, 5000),
      currency: 'USD',
      paymentStatus: this.getRandomElement(['pending', 'partial', 'paid', 'refunded', 'failed']),
      bookingDate: this.getRandomDate(new Date(2023, 0, 1), new Date()),
      travelDate,
      specialRequests: this.getRandomBoolean() ? 'Vegetarian meals preferred' : undefined,
      documents: [],
      notifications: [],
      createdAt: this.getRandomDate(new Date(2023, 0, 1), new Date()),
      updatedAt: new Date()
    };
  }

  static generateTravelers(count: number): Traveler[] {
    const travelers: Traveler[] = [];
    
    for (let i = 0; i < count; i++) {
      const firstName = this.getRandomElement(this.firstNames);
      const lastName = this.getRandomElement(this.lastNames);
      
      travelers.push({
        id: this.generateId(),
        firstName,
        lastName,
        dateOfBirth: this.getRandomDate(new Date(1950, 0, 1), new Date(2010, 0, 1)),
        nationality: this.getRandomElement(['American', 'British', 'Canadian', 'Australian', 'German']),
        passportNumber: `P${this.getRandomNumber(10000000, 99999999)}`,
        passportExpiry: this.getRandomDate(new Date(), new Date(2030, 0, 1)),
        dietaryRestrictions: this.getRandomBoolean() ? ['Vegetarian'] : undefined,
        medicalConditions: this.getRandomBoolean() ? ['None'] : undefined,
        emergencyContact: {
          name: `${this.getRandomElement(this.firstNames)} ${this.getRandomElement(this.lastNames)}`,
          relationship: this.getRandomElement(['spouse', 'parent', 'sibling', 'friend']),
          phone: `+1${this.getRandomNumber(**********, **********)}`,
          email: `emergency${this.getRandomNumber(1, 1000)}@email.com`
        }
      });
    }
    
    return travelers;
  }

  // Notification generator
  static generateNotification(userId?: string): Notification {
    const notifications = [
      { title: 'Booking Confirmed', message: 'Your travel booking has been confirmed!' },
      { title: 'Payment Reminder', message: 'Payment due for your upcoming trip.' },
      { title: 'Trip Update', message: 'Important update about your travel itinerary.' },
      { title: 'Weather Alert', message: 'Weather conditions update for your destination.' },
      { title: 'Document Reminder', message: 'Please upload required travel documents.' }
    ];
    
    const notification = this.getRandomElement(notifications);
    
    return {
      id: this.generateId(),
      userId: userId || this.generateId(),
      type: this.getRandomElement(['booking_confirmation', 'payment_reminder', 'trip_update', 'weather_alert', 'document_reminder']),
      title: notification.title,
      message: notification.message,
      isRead: this.getRandomBoolean(),
      priority: this.getRandomElement(['low', 'medium', 'high', 'urgent']),
      actionUrl: this.getRandomBoolean() ? '/bookings/123' : undefined,
      createdAt: this.getRandomDate(new Date(2023, 0, 1), new Date()),
      readAt: this.getRandomBoolean() ? this.getRandomDate(new Date(2023, 0, 1), new Date()) : undefined
    };
  }

  // Analytics generator
  static generateAnalyticsData(): AnalyticsData {
    return {
      totalBookings: this.getRandomNumber(1000, 5000),
      totalRevenue: this.getRandomNumber(500000, 2000000),
      activeAgents: this.getRandomNumber(50, 200),
      popularDestinations: this.generateDestinationStats(),
      monthlyStats: this.generateMonthlyStats(),
      conversionRate: Math.round((Math.random() * 0.1 + 0.05) * 100) / 100 // 5-15%
    };
  }

  static generateDestinationStats(): DestinationStats[] {
    return this.destinations.slice(0, 10).map(destination => ({
      destination,
      bookings: this.getRandomNumber(50, 300),
      revenue: this.getRandomNumber(25000, 150000)
    }));
  }

  static generateMonthlyStats(): MonthlyStats[] {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    return months.map(month => ({
      month,
      bookings: this.getRandomNumber(100, 500),
      revenue: this.getRandomNumber(50000, 250000),
      newUsers: this.getRandomNumber(20, 100)
    }));
  }

  // Bulk generators
  static generateUsers(count: number): User[] {
    const users: User[] = [];
    
    // Generate 1 super admin
    users.push(this.generateSuperAdmin());
    
    // Generate travel agents (20% of total)
    const agentCount = Math.floor(count * 0.2);
    for (let i = 0; i < agentCount; i++) {
      users.push(this.generateTravelAgent());
    }
    
    // Generate consumers (remaining)
    const consumerCount = count - 1 - agentCount;
    for (let i = 0; i < consumerCount; i++) {
      users.push(this.generateConsumer());
    }
    
    return users;
  }

  static generateTravelPackages(count: number, agentIds?: string[]): TravelPackage[] {
    const packages: TravelPackage[] = [];
    
    for (let i = 0; i < count; i++) {
      const agentId = agentIds ? this.getRandomElement(agentIds) : undefined;
      packages.push(this.generateTravelPackage(agentId));
    }
    
    return packages;
  }

  static generateBookings(count: number, packageIds?: string[], consumerIds?: string[], agentIds?: string[]): Booking[] {
    const bookings: Booking[] = [];
    
    for (let i = 0; i < count; i++) {
      const packageId = packageIds ? this.getRandomElement(packageIds) : undefined;
      const consumerId = consumerIds ? this.getRandomElement(consumerIds) : undefined;
      const agentId = agentIds ? this.getRandomElement(agentIds) : undefined;
      
      bookings.push(this.generateBooking(packageId, consumerId, agentId));
    }
    
    return bookings;
  }

  static generateNotifications(count: number, userIds?: string[]): Notification[] {
    const notifications: Notification[] = [];
    
    for (let i = 0; i < count; i++) {
      const userId = userIds ? this.getRandomElement(userIds) : undefined;
      notifications.push(this.generateNotification(userId));
    }
    
    return notifications;
  }
}

// Export the generator class
export default MockDataGenerator;
export { MockDataGenerator };

// Export pre-generated mock data
export * from './data';
export { default as mockData } from './data';