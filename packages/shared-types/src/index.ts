// Core User Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  avatar?: string;
  phone?: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export type UserRole = 'super_admin' | 'travel_agent' | 'consumer';

export interface SuperAdmin extends User {
  role: 'super_admin';
  permissions: AdminPermission[];
}

export interface TravelAgent extends User {
  role: 'travel_agent';
  agencyName: string;
  licenseNumber: string;
  verificationStatus: 'pending' | 'verified' | 'rejected';
  specializations: string[];
  rating: number;
  totalBookings: number;
}

export interface Consumer extends User {
  role: 'consumer';
  preferences: TravelPreferences;
  emergencyContact?: EmergencyContact;
}

export type AdminPermission = 
  | 'manage_agents' 
  | 'view_analytics' 
  | 'system_config' 
  | 'audit_logs';

// Travel Package Types
export interface TravelPackage {
  id: string;
  title: string;
  description: string;
  destination: string;
  duration: number; // in days
  maxGroupSize: number;
  minAge: number;
  difficulty: 'easy' | 'moderate' | 'challenging';
  category: PackageCategory;
  pricing: PricingTier[];
  itinerary: ItineraryItem[];
  inclusions: string[];
  exclusions: string[];
  images: string[];
  agentId: string;
  status: 'draft' | 'published' | 'archived';
  createdAt: Date;
  updatedAt: Date;
}

export type PackageCategory = 
  | 'adventure' 
  | 'cultural' 
  | 'relaxation' 
  | 'business' 
  | 'family' 
  | 'luxury';

export interface PricingTier {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  currency: string;
  seasonalMultiplier?: number;
  groupDiscounts?: GroupDiscount[];
}

export interface GroupDiscount {
  minSize: number;
  discountPercentage: number;
}

// Itinerary Types
export interface ItineraryItem {
  id: string;
  day: number;
  title: string;
  description: string;
  activities: Activity[];
  accommodation?: Accommodation;
  meals: MealType[];
  transportation?: Transportation;
  location: Location;
}

export interface Activity {
  id: string;
  name: string;
  description: string;
  duration: number; // in minutes
  type: ActivityType;
  cost?: number;
  isOptional: boolean;
  requirements?: string[];
}

export type ActivityType = 
  | 'sightseeing' 
  | 'adventure' 
  | 'cultural' 
  | 'dining' 
  | 'shopping' 
  | 'relaxation';

export type MealType = 'breakfast' | 'lunch' | 'dinner' | 'snack';

// Booking Types
export interface Booking {
  id: string;
  packageId: string;
  consumerId: string;
  agentId: string;
  status: BookingStatus;
  travelers: Traveler[];
  totalAmount: number;
  currency: string;
  paymentStatus: PaymentStatus;
  bookingDate: Date;
  travelDate: Date;
  specialRequests?: string;
  documents: BookingDocument[];
  notifications: Notification[];
  createdAt: Date;
  updatedAt: Date;
}

export type BookingStatus = 
  | 'pending' 
  | 'confirmed' 
  | 'cancelled' 
  | 'completed' 
  | 'in_progress';

export type PaymentStatus = 
  | 'pending' 
  | 'partial' 
  | 'paid' 
  | 'refunded' 
  | 'failed';

export interface Traveler {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  nationality: string;
  passportNumber?: string;
  passportExpiry?: Date;
  dietaryRestrictions?: string[];
  medicalConditions?: string[];
  emergencyContact: EmergencyContact;
}

export interface BookingDocument {
  id: string;
  type: DocumentType;
  name: string;
  url: string;
  uploadedAt: Date;
  expiryDate?: Date;
}

export type DocumentType = 
  | 'passport' 
  | 'visa' 
  | 'ticket' 
  | 'insurance' 
  | 'vaccination' 
  | 'other';

// Location and Accommodation Types
export interface Location {
  id: string;
  name: string;
  address: string;
  city: string;
  country: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  timezone: string;
}

export interface Accommodation {
  id: string;
  name: string;
  type: AccommodationType;
  rating: number;
  location: Location;
  amenities: string[];
  checkIn: string; // time format
  checkOut: string; // time format
  images: string[];
}

export type AccommodationType = 
  | 'hotel' 
  | 'resort' 
  | 'hostel' 
  | 'apartment' 
  | 'villa' 
  | 'camping';

export interface Transportation {
  id: string;
  type: TransportationType;
  provider: string;
  departureTime: string;
  arrivalTime: string;
  from: Location;
  to: Location;
  seatClass?: string;
  ticketNumber?: string;
}

export type TransportationType = 
  | 'flight' 
  | 'train' 
  | 'bus' 
  | 'car' 
  | 'boat' 
  | 'walking';

// Notification Types
export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  actionUrl?: string;
  createdAt: Date;
  readAt?: Date;
}

export type NotificationType = 
  | 'booking_confirmation' 
  | 'payment_reminder' 
  | 'trip_update' 
  | 'weather_alert' 
  | 'document_reminder' 
  | 'system_announcement';

// Utility Types
export interface TravelPreferences {
  budgetRange: {
    min: number;
    max: number;
    currency: string;
  };
  preferredDestinations: string[];
  travelStyle: 'budget' | 'mid_range' | 'luxury';
  groupSize: 'solo' | 'couple' | 'family' | 'group';
  interests: string[];
  dietaryRestrictions: string[];
  accessibilityNeeds: string[];
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
  email?: string;
  address?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: Date;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Authentication Types
export interface AuthToken {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: 'Bearer';
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  avatar?: string;
}

// Form Types
export interface ContactForm {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
}

export interface BookingForm {
  packageId: string;
  travelers: Omit<Traveler, 'id'>[];
  travelDate: Date;
  specialRequests?: string;
  emergencyContact: EmergencyContact;
}

// Analytics Types
export interface AnalyticsData {
  totalBookings: number;
  totalRevenue: number;
  activeAgents: number;
  popularDestinations: DestinationStats[];
  monthlyStats: MonthlyStats[];
  conversionRate: number;
}

export interface DestinationStats {
  destination: string;
  bookings: number;
  revenue: number;
}

export interface MonthlyStats {
  month: string;
  bookings: number;
  revenue: number;
  newUsers: number;
}