{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/pages/*": ["./src/pages/*"], "@/hooks/*": ["./src/hooks/*"], "@/utils/*": ["./src/utils/*"], "@/store/*": ["./src/store/*"], "@/types/*": ["./src/types/*"], "@travelease/shared-types": ["../shared-types/src/index.ts"], "@travelease/shared-ui": ["../shared-ui/src/index.ts"], "@travelease/mock-data": ["../mock-data/src/index.ts"]}}, "include": ["src", "vite.config.ts"], "references": [{"path": "./tsconfig.node.json"}]}