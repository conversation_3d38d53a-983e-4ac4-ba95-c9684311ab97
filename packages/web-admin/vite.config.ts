// Vite configuration for TravelEase Admin Portal
// Note: This file requires Node.js and Vite dependencies to be installed

export default {
  plugins: [],
  resolve: {
    alias: {
      '@': './src',
      '@/components': './src/components',
      '@/pages': './src/pages',
      '@/hooks': './src/hooks',
      '@/utils': './src/utils',
      '@/store': './src/store',
      '@/types': './src/types',
      '@travelease/shared-types': '../shared-types/src',
      '@travelease/shared-ui': '../shared-ui/src',
      '@travelease/mock-data': '../mock-data/src',
    },
  },
  server: {
    port: 3001,
    host: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
};

// To use this configuration properly, install dependencies:
// npm install vite @vitejs/plugin-react react react-dom
// Then uncomment and use the proper imports:
/*
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  // ... rest of configuration
});
*/