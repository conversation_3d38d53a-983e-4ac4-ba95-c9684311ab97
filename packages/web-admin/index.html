<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="TravelEase Admin Portal - Manage your travel business" />
    <title>TravelEase Admin Portal</title>
    
    <!-- Preconnect to Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS CDN for development -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: {
                50: '#eff6ff',
                100: '#dbeafe',
                200: '#bfdbfe',
                300: '#93c5fd',
                400: '#60a5fa',
                500: '#3b82f6',
                600: '#2563eb',
                700: '#1d4ed8',
                800: '#1e40af',
                900: '#1e3a8a'
              }
            },
            fontFamily: {
              sans: ['Inter', 'sans-serif'],
              mono: ['JetBrains Mono', 'monospace']
            }
          }
        }
      }
    </script>
    
    <style>
      .sidebar-transition {
        transition: transform 0.3s ease-in-out;
      }
      
      .fade-in {
        animation: fadeIn 0.5s ease-in-out;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      .card-hover {
        transition: all 0.2s ease-in-out;
      }
      
      .card-hover:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <div id="root">
      <!-- Loading state -->
      <div class="min-h-screen flex items-center justify-center">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <h2 class="text-xl font-semibold text-gray-900 mb-2">TravelEase Admin Portal</h2>
          <p class="text-gray-600">Loading application...</p>
        </div>
      </div>
    </div>
    
    <!-- Demo Admin Portal -->
    <script>
      // Simple demo implementation
      document.addEventListener('DOMContentLoaded', function() {
        const root = document.getElementById('root');
        
        // Demo admin portal HTML
        root.innerHTML = `
          <div class="min-h-screen bg-gray-50">
            <!-- Sidebar -->
            <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out" id="sidebar">
              <div class="flex items-center justify-center h-16 px-4 bg-primary-600">
                <h1 class="text-xl font-bold text-white">TravelEase Admin</h1>
              </div>
              
              <nav class="mt-8">
                <div class="px-4 space-y-2">
                  <a href="#" class="nav-link active" data-page="dashboard">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    Dashboard
                  </a>
                  <a href="#" class="nav-link" data-page="users">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    Users
                  </a>
                  <a href="#" class="nav-link" data-page="packages">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Packages
                  </a>
                  <a href="#" class="nav-link" data-page="bookings">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    Bookings
                  </a>
                  <a href="#" class="nav-link" data-page="analytics">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Analytics
                  </a>
                  <a href="#" class="nav-link" data-page="settings">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Settings
                  </a>
                </div>
              </nav>
            </div>
            
            <!-- Main Content -->
            <div class="ml-64">
              <!-- Header -->
              <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-4">
                  <div class="flex items-center space-x-4">
                    <button class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100" id="mobile-menu-button">
                      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                      </svg>
                    </button>
                    <div class="relative">
                      <input type="text" placeholder="Search..." class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                      <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                      </svg>
                    </div>
                  </div>
                  
                  <div class="flex items-center space-x-4">
                    <button class="p-2 text-gray-400 hover:text-gray-500">
                      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"></path>
                      </svg>
                    </button>
                    <div class="flex items-center space-x-3">
                      <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-white">AU</span>
                      </div>
                      <div class="hidden md:block">
                        <p class="text-sm font-medium text-gray-900">Admin User</p>
                        <p class="text-xs text-gray-500"><EMAIL></p>
                      </div>
                    </div>
                  </div>
                </div>
              </header>
              
              <!-- Page Content -->
              <main class="p-6" id="main-content">
                <div id="dashboard-content" class="page-content">
                  <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
                    <p class="text-gray-600">Welcome to the TravelEase Admin Portal</p>
                  </div>
                  
                  <!-- Stats Grid -->
                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-lg shadow card-hover">
                      <div class="flex items-center justify-between">
                        <div>
                          <p class="text-sm font-medium text-gray-600">Total Users</p>
                          <p class="text-2xl font-bold text-gray-900">12,345</p>
                          <p class="text-sm text-green-600">+12% from last month</p>
                        </div>
                        <div class="p-3 bg-blue-50 rounded-lg">
                          <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                    
                    <div class="bg-white p-6 rounded-lg shadow card-hover">
                      <div class="flex items-center justify-between">
                        <div>
                          <p class="text-sm font-medium text-gray-600">Active Packages</p>
                          <p class="text-2xl font-bold text-gray-900">89</p>
                          <p class="text-sm text-green-600">+5% from last month</p>
                        </div>
                        <div class="p-3 bg-blue-50 rounded-lg">
                          <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                    
                    <div class="bg-white p-6 rounded-lg shadow card-hover">
                      <div class="flex items-center justify-between">
                        <div>
                          <p class="text-sm font-medium text-gray-600">Total Bookings</p>
                          <p class="text-2xl font-bold text-gray-900">2,456</p>
                          <p class="text-sm text-green-600">+18% from last month</p>
                        </div>
                        <div class="p-3 bg-blue-50 rounded-lg">
                          <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                    
                    <div class="bg-white p-6 rounded-lg shadow card-hover">
                      <div class="flex items-center justify-between">
                        <div>
                          <p class="text-sm font-medium text-gray-600">Revenue</p>
                          <p class="text-2xl font-bold text-gray-900">$124,567</p>
                          <p class="text-sm text-green-600">+23% from last month</p>
                        </div>
                        <div class="p-3 bg-blue-50 rounded-lg">
                          <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Recent Activity -->
                  <div class="bg-white rounded-lg shadow">
                    <div class="p-6">
                      <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                      <div class="space-y-4">
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                          <div>
                            <p class="font-medium text-gray-900">New user registration</p>
                            <p class="text-sm text-gray-600">by John Doe</p>
                          </div>
                          <p class="text-sm text-gray-500">2 minutes ago</p>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                          <div>
                            <p class="font-medium text-gray-900">Package booking</p>
                            <p class="text-sm text-gray-600">by Jane Smith</p>
                          </div>
                          <p class="text-sm text-gray-500">15 minutes ago</p>
                        </div>
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                          <div>
                            <p class="font-medium text-gray-900">Payment received</p>
                            <p class="text-sm text-gray-600">by Mike Johnson</p>
                          </div>
                          <p class="text-sm text-gray-500">1 hour ago</p>
                        </div>
                        <div class="flex items-center justify-between py-2">
                          <div>
                            <p class="font-medium text-gray-900">New travel package created</p>
                            <p class="text-sm text-gray-600">by Admin</p>
                          </div>
                          <p class="text-sm text-gray-500">2 hours ago</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div id="users-content" class="page-content hidden">
                  <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Users</h1>
                    <p class="text-gray-600">Manage system users and their permissions</p>
                  </div>
                  
                  <div class="bg-white rounded-lg shadow p-6">
                    <p class="text-gray-600">Users management interface will be implemented here.</p>
                    <p class="text-sm text-gray-500 mt-2">Features: User list, search, filter, edit, delete, role management</p>
                  </div>
                </div>
                
                <div id="packages-content" class="page-content hidden">
                  <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Travel Packages</h1>
                    <p class="text-gray-600">Manage travel packages and destinations</p>
                  </div>
                  
                  <div class="bg-white rounded-lg shadow p-6">
                    <p class="text-gray-600">Package management interface will be implemented here.</p>
                    <p class="text-sm text-gray-500 mt-2">Features: Package list, create, edit, delete, pricing, itinerary management</p>
                  </div>
                </div>
                
                <div id="bookings-content" class="page-content hidden">
                  <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Bookings</h1>
                    <p class="text-gray-600">Manage customer bookings and reservations</p>
                  </div>
                  
                  <div class="bg-white rounded-lg shadow p-6">
                    <p class="text-gray-600">Booking management interface will be implemented here.</p>
                    <p class="text-sm text-gray-500 mt-2">Features: Booking list, status updates, payment tracking, customer communication</p>
                  </div>
                </div>
                
                <div id="analytics-content" class="page-content hidden">
                  <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Analytics</h1>
                    <p class="text-gray-600">Track performance and business metrics</p>
                  </div>
                  
                  <div class="bg-white rounded-lg shadow p-6">
                    <p class="text-gray-600">Analytics dashboard will be implemented here.</p>
                    <p class="text-sm text-gray-500 mt-2">Features: Revenue charts, booking trends, user analytics, performance metrics</p>
                  </div>
                </div>
                
                <div id="settings-content" class="page-content hidden">
                  <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Settings</h1>
                    <p class="text-gray-600">Manage your account and system preferences</p>
                  </div>
                  
                  <div class="bg-white rounded-lg shadow p-6">
                    <p class="text-gray-600">Settings interface will be implemented here.</p>
                    <p class="text-sm text-gray-500 mt-2">Features: Profile settings, notifications, security, system configuration</p>
                  </div>
                </div>
              </main>
            </div>
          </div>
        `;
        
        // Add navigation functionality
        const navLinks = document.querySelectorAll('.nav-link');
        const pageContents = document.querySelectorAll('.page-content');
        
        navLinks.forEach(link => {
          link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));
            // Add active class to clicked link
            this.classList.add('active');
            
            // Hide all page contents
            pageContents.forEach(content => content.classList.add('hidden'));
            
            // Show selected page content
            const page = this.getAttribute('data-page');
            const targetContent = document.getElementById(page + '-content');
            if (targetContent) {
              targetContent.classList.remove('hidden');
              targetContent.classList.add('fade-in');
            }
          });
        });
      });
    </script>
    
    <style>
      .nav-link {
        @apply flex items-center space-x-3 px-4 py-2 text-sm font-medium text-gray-600 rounded-lg hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200;
      }
      
      .nav-link.active {
        @apply bg-primary-50 text-primary-700 border-r-2 border-primary-600;
      }
      
      .nav-link svg {
        @apply flex-shrink-0;
      }
    </style>
  </body>
</html>