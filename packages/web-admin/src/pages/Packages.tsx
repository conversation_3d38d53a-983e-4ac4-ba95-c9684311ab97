import React, { useState } from 'react';
import { Search, Filter, Plus, Edit, Trash2, Eye, MapPin, Calendar, DollarSign } from 'lucide-react';
import { Button, Input, Card } from '@travelease/shared-ui';
import { TravelPackage } from '@travelease/shared-types';

interface PackageCardProps {
  package: TravelPackage;
  onEdit: (pkg: TravelPackage) => void;
  onDelete: (packageId: string) => void;
  onView: (pkg: TravelPackage) => void;
}

const PackageCard: React.FC<PackageCardProps> = ({ package: pkg, onEdit, onDelete, onView }) => {
  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow">
      <div className="aspect-w-16 aspect-h-9 bg-gray-200">
        <img
          src={pkg.images[0] || '/placeholder-image.jpg'}
          alt={pkg.title}
          className="w-full h-48 object-cover"
        />
      </div>
      <div className="p-6">
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
            {pkg.title}
          </h3>
          <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
            pkg.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {pkg.isActive ? 'Active' : 'Inactive'}
          </span>
        </div>
        
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {pkg.description}
        </p>
        
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-500">
            <MapPin className="h-4 w-4 mr-2" />
            {pkg.destination}
          </div>
          <div className="flex items-center text-sm text-gray-500">
            <Calendar className="h-4 w-4 mr-2" />
            {pkg.duration} days
          </div>
          <div className="flex items-center text-sm text-gray-900 font-semibold">
            <DollarSign className="h-4 w-4 mr-2" />
            ${pkg.price.toLocaleString()}
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            <button
              onClick={() => onView(pkg)}
              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            >
              <Eye className="h-4 w-4" />
            </button>
            <button
              onClick={() => onEdit(pkg)}
              className="p-2 text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors"
            >
              <Edit className="h-4 w-4" />
            </button>
            <button
              onClick={() => onDelete(pkg.id)}
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
          <span className="text-xs text-gray-500">
            Created {new Date(pkg.createdAt).toLocaleDateString()}
          </span>
        </div>
      </div>
    </Card>
  );
};

const Packages: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [packages] = useState<TravelPackage[]>([
    {
      id: '1',
      title: 'Tropical Paradise Getaway',
      description: 'Experience the ultimate tropical vacation with pristine beaches, crystal clear waters, and luxury accommodations.',
      destination: 'Maldives',
      duration: 7,
      price: 2999,
      images: ['/maldives.jpg'],
      itinerary: [],
      inclusions: ['Flights', 'Accommodation', 'Meals', 'Activities'],
      exclusions: ['Personal expenses', 'Travel insurance'],
      isActive: true,
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15')
    },
    {
      id: '2',
      title: 'European Cultural Tour',
      description: 'Discover the rich history and culture of Europe with guided tours through iconic cities and landmarks.',
      destination: 'Europe',
      duration: 14,
      price: 4599,
      images: ['/europe.jpg'],
      itinerary: [],
      inclusions: ['Flights', 'Accommodation', 'Guided tours', 'Some meals'],
      exclusions: ['Personal expenses', 'Optional activities'],
      isActive: true,
      createdAt: new Date('2024-01-10'),
      updatedAt: new Date('2024-01-10')
    },
    {
      id: '3',
      title: 'Adventure Mountain Trek',
      description: 'Challenge yourself with an exciting mountain trekking adventure through breathtaking landscapes.',
      destination: 'Nepal',
      duration: 10,
      price: 1899,
      images: ['/nepal.jpg'],
      itinerary: [],
      inclusions: ['Accommodation', 'Meals', 'Guide', 'Equipment'],
      exclusions: ['Flights', 'Personal gear', 'Travel insurance'],
      isActive: false,
      createdAt: new Date('2024-01-05'),
      updatedAt: new Date('2024-01-05')
    }
  ]);

  const filteredPackages = packages.filter(pkg => {
    const matchesSearch = 
      pkg.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pkg.destination.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pkg.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = 
      filterStatus === 'all' || 
      (filterStatus === 'active' && pkg.isActive) ||
      (filterStatus === 'inactive' && !pkg.isActive);
    
    return matchesSearch && matchesStatus;
  });

  const handleEdit = (pkg: TravelPackage) => {
    console.log('Edit package:', pkg);
    // TODO: Implement edit functionality
  };

  const handleDelete = (packageId: string) => {
    console.log('Delete package:', packageId);
    // TODO: Implement delete functionality
  };

  const handleView = (pkg: TravelPackage) => {
    console.log('View package:', pkg);
    // TODO: Implement view functionality
  };

  const handleAddPackage = () => {
    console.log('Add new package');
    // TODO: Implement add package functionality
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Travel Packages</h1>
          <p className="text-gray-600">Manage travel packages and destinations</p>
        </div>
        <Button onClick={handleAddPackage} className="flex items-center space-x-2">
          <Plus className="h-4 w-4" />
          <span>Add Package</span>
        </Button>
      </div>

      {/* Filters */}
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search packages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Package Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-3 bg-blue-50 rounded-lg">
              <MapPin className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Packages</p>
              <p className="text-2xl font-bold text-gray-900">{packages.length}</p>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-3 bg-green-50 rounded-lg">
              <Calendar className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Packages</p>
              <p className="text-2xl font-bold text-gray-900">
                {packages.filter(pkg => pkg.isActive).length}
              </p>
            </div>
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-3 bg-yellow-50 rounded-lg">
              <DollarSign className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg. Price</p>
              <p className="text-2xl font-bold text-gray-900">
                ${Math.round(packages.reduce((sum, pkg) => sum + pkg.price, 0) / packages.length).toLocaleString()}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Packages Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredPackages.map((pkg) => (
          <PackageCard
            key={pkg.id}
            package={pkg}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onView={handleView}
          />
        ))}
      </div>

      {filteredPackages.length === 0 && (
        <Card className="p-12 text-center">
          <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No packages found</h3>
          <p className="text-gray-600 mb-4">Try adjusting your search or filter criteria</p>
          <Button onClick={handleAddPackage}>
            Create New Package
          </Button>
        </Card>
      )}
    </div>
  );
};

export default Packages;