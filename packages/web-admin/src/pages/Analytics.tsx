import React, { useState } from 'react';
import { BarChart3, TrendingUp, Users, DollarSign, Calendar, MapPin } from 'lucide-react';
import { Card } from '@travelease/shared-ui';

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative';
  icon: React.ReactNode;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, change, changeType, icon }) => {
  return (
    <Card className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-3xl font-bold text-gray-900">{value}</p>
          <p className={`text-sm font-medium ${
            changeType === 'positive' ? 'text-green-600' : 'text-red-600'
          }`}>
            {change}
          </p>
        </div>
        <div className="p-3 bg-blue-50 rounded-lg">
          {icon}
        </div>
      </div>
    </Card>
  );
};

const Analytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState('30d');

  const metrics = [
    {
      title: 'Total Revenue',
      value: '$245,670',
      change: '+12.5% vs last month',
      changeType: 'positive' as const,
      icon: <DollarSign className="h-6 w-6 text-blue-600" />
    },
    {
      title: 'New Bookings',
      value: '1,234',
      change: '+8.2% vs last month',
      changeType: 'positive' as const,
      icon: <Calendar className="h-6 w-6 text-blue-600" />
    },
    {
      title: 'Active Users',
      value: '8,945',
      change: '+15.3% vs last month',
      changeType: 'positive' as const,
      icon: <Users className="h-6 w-6 text-blue-600" />
    },
    {
      title: 'Conversion Rate',
      value: '3.2%',
      change: '-0.5% vs last month',
      changeType: 'negative' as const,
      icon: <TrendingUp className="h-6 w-6 text-blue-600" />
    }
  ];

  const topDestinations = [
    { name: 'Maldives', bookings: 145, revenue: '$435,000' },
    { name: 'Europe', bookings: 98, revenue: '$450,200' },
    { name: 'Nepal', bookings: 67, revenue: '$127,330' },
    { name: 'Thailand', bookings: 54, revenue: '$162,000' },
    { name: 'Japan', bookings: 43, revenue: '$215,000' }
  ];

  const recentActivity = [
    { type: 'booking', description: 'New booking for Maldives package', time: '2 minutes ago' },
    { type: 'user', description: 'New user registration', time: '15 minutes ago' },
    { type: 'payment', description: 'Payment received for booking #12345', time: '1 hour ago' },
    { type: 'package', description: 'New travel package published', time: '2 hours ago' },
    { type: 'review', description: 'New 5-star review received', time: '3 hours ago' }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600">Track performance and business metrics</p>
        </div>
        <div className="flex items-center space-x-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <MetricCard key={index} {...metric} />
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Revenue Trend</h3>
            <BarChart3 className="h-5 w-5 text-gray-400" />
          </div>
          <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Revenue chart will be implemented with Recharts</p>
              <p className="text-sm text-gray-400 mt-2">Line chart showing revenue over time</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Booking Distribution</h3>
            <TrendingUp className="h-5 w-5 text-gray-400" />
          </div>
          <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Booking distribution chart will be implemented</p>
              <p className="text-sm text-gray-400 mt-2">Pie chart showing bookings by package type</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Top Destinations and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Top Destinations</h3>
          <div className="space-y-4">
            {topDestinations.map((destination, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <MapPin className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{destination.name}</p>
                    <p className="text-sm text-gray-600">{destination.bookings} bookings</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">{destination.revenue}</p>
                  <p className="text-sm text-gray-600">Revenue</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Recent Activity</h3>
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg ${
                  activity.type === 'booking' ? 'bg-green-100' :
                  activity.type === 'user' ? 'bg-blue-100' :
                  activity.type === 'payment' ? 'bg-yellow-100' :
                  activity.type === 'package' ? 'bg-purple-100' :
                  'bg-pink-100'
                }`}>
                  {activity.type === 'booking' && <Calendar className="h-4 w-4 text-green-600" />}
                  {activity.type === 'user' && <Users className="h-4 w-4 text-blue-600" />}
                  {activity.type === 'payment' && <DollarSign className="h-4 w-4 text-yellow-600" />}
                  {activity.type === 'package' && <MapPin className="h-4 w-4 text-purple-600" />}
                  {activity.type === 'review' && <TrendingUp className="h-4 w-4 text-pink-600" />}
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{activity.description}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Performance Summary */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Performance Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="p-4 bg-green-50 rounded-lg mb-4">
              <TrendingUp className="h-8 w-8 text-green-600 mx-auto" />
            </div>
            <h4 className="font-semibold text-gray-900">Growth Rate</h4>
            <p className="text-2xl font-bold text-green-600">+23.5%</p>
            <p className="text-sm text-gray-600">Month over month</p>
          </div>
          <div className="text-center">
            <div className="p-4 bg-blue-50 rounded-lg mb-4">
              <Users className="h-8 w-8 text-blue-600 mx-auto" />
            </div>
            <h4 className="font-semibold text-gray-900">Customer Satisfaction</h4>
            <p className="text-2xl font-bold text-blue-600">4.8/5</p>
            <p className="text-sm text-gray-600">Average rating</p>
          </div>
          <div className="text-center">
            <div className="p-4 bg-purple-50 rounded-lg mb-4">
              <BarChart3 className="h-8 w-8 text-purple-600 mx-auto" />
            </div>
            <h4 className="font-semibold text-gray-900">Market Share</h4>
            <p className="text-2xl font-bold text-purple-600">12.3%</p>
            <p className="text-sm text-gray-600">In travel industry</p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default Analytics;