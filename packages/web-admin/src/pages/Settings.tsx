import React, { useState } from 'react';
import { Save, User, Bell, Shield, Globe, Database, Mail } from 'lucide-react';
import { Button, Input, Card } from '@travelease/shared-ui';

interface SettingsSectionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  children: React.ReactNode;
}

const SettingsSection: React.FC<SettingsSectionProps> = ({ title, description, icon, children }) => {
  return (
    <Card className="p-6">
      <div className="flex items-center space-x-3 mb-4">
        <div className="p-2 bg-blue-50 rounded-lg">
          {icon}
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
      </div>
      {children}
    </Card>
  );
};

const Settings: React.FC = () => {
  const [profile, setProfile] = useState({
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    phone: '+****************',
    timezone: 'UTC-5'
  });

  const [notifications, setNotifications] = useState({
    emailBookings: true,
    emailPayments: true,
    emailUsers: false,
    pushBookings: true,
    pushPayments: false,
    pushUsers: false
  });

  const [security, setSecurity] = useState({
    twoFactorEnabled: false,
    sessionTimeout: '30',
    passwordExpiry: '90'
  });

  const [system, setSystem] = useState({
    maintenanceMode: false,
    debugMode: false,
    apiRateLimit: '1000',
    maxFileSize: '10'
  });

  const handleProfileSave = () => {
    console.log('Saving profile:', profile);
    // TODO: Implement profile save
  };

  const handleNotificationsSave = () => {
    console.log('Saving notifications:', notifications);
    // TODO: Implement notifications save
  };

  const handleSecuritySave = () => {
    console.log('Saving security:', security);
    // TODO: Implement security save
  };

  const handleSystemSave = () => {
    console.log('Saving system:', system);
    // TODO: Implement system save
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Manage your account and system preferences</p>
      </div>

      {/* Profile Settings */}
      <SettingsSection
        title="Profile Settings"
        description="Update your personal information and preferences"
        icon={<User className="h-5 w-5 text-blue-600" />}
      >
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                First Name
              </label>
              <Input
                type="text"
                value={profile.firstName}
                onChange={(e) => setProfile({ ...profile, firstName: e.target.value })}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Last Name
              </label>
              <Input
                type="text"
                value={profile.lastName}
                onChange={(e) => setProfile({ ...profile, lastName: e.target.value })}
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <Input
              type="email"
              value={profile.email}
              onChange={(e) => setProfile({ ...profile, email: e.target.value })}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Phone Number
              </label>
              <Input
                type="tel"
                value={profile.phone}
                onChange={(e) => setProfile({ ...profile, phone: e.target.value })}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Timezone
              </label>
              <select
                value={profile.timezone}
                onChange={(e) => setProfile({ ...profile, timezone: e.target.value })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="UTC-8">Pacific Time (UTC-8)</option>
                <option value="UTC-7">Mountain Time (UTC-7)</option>
                <option value="UTC-6">Central Time (UTC-6)</option>
                <option value="UTC-5">Eastern Time (UTC-5)</option>
                <option value="UTC+0">UTC</option>
              </select>
            </div>
          </div>
          <div className="flex justify-end">
            <Button onClick={handleProfileSave} className="flex items-center space-x-2">
              <Save className="h-4 w-4" />
              <span>Save Profile</span>
            </Button>
          </div>
        </div>
      </SettingsSection>

      {/* Notification Settings */}
      <SettingsSection
        title="Notification Settings"
        description="Configure how you receive notifications"
        icon={<Bell className="h-5 w-5 text-blue-600" />}
      >
        <div className="space-y-6">
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Email Notifications</h4>
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={notifications.emailBookings}
                  onChange={(e) => setNotifications({ ...notifications, emailBookings: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-3 text-sm text-gray-700">New bookings</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={notifications.emailPayments}
                  onChange={(e) => setNotifications({ ...notifications, emailPayments: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-3 text-sm text-gray-700">Payment confirmations</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={notifications.emailUsers}
                  onChange={(e) => setNotifications({ ...notifications, emailUsers: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-3 text-sm text-gray-700">New user registrations</span>
              </label>
            </div>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Push Notifications</h4>
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={notifications.pushBookings}
                  onChange={(e) => setNotifications({ ...notifications, pushBookings: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-3 text-sm text-gray-700">New bookings</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={notifications.pushPayments}
                  onChange={(e) => setNotifications({ ...notifications, pushPayments: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-3 text-sm text-gray-700">Payment confirmations</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={notifications.pushUsers}
                  onChange={(e) => setNotifications({ ...notifications, pushUsers: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-3 text-sm text-gray-700">New user registrations</span>
              </label>
            </div>
          </div>
          <div className="flex justify-end">
            <Button onClick={handleNotificationsSave} className="flex items-center space-x-2">
              <Save className="h-4 w-4" />
              <span>Save Notifications</span>
            </Button>
          </div>
        </div>
      </SettingsSection>

      {/* Security Settings */}
      <SettingsSection
        title="Security Settings"
        description="Manage security and authentication preferences"
        icon={<Shield className="h-5 w-5 text-blue-600" />}
      >
        <div className="space-y-4">
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={security.twoFactorEnabled}
                onChange={(e) => setSecurity({ ...security, twoFactorEnabled: e.target.checked })}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-3 text-sm text-gray-700">Enable Two-Factor Authentication</span>
            </label>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Session Timeout (minutes)
              </label>
              <Input
                type="number"
                value={security.sessionTimeout}
                onChange={(e) => setSecurity({ ...security, sessionTimeout: e.target.value })}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password Expiry (days)
              </label>
              <Input
                type="number"
                value={security.passwordExpiry}
                onChange={(e) => setSecurity({ ...security, passwordExpiry: e.target.value })}
              />
            </div>
          </div>
          <div className="flex justify-end">
            <Button onClick={handleSecuritySave} className="flex items-center space-x-2">
              <Save className="h-4 w-4" />
              <span>Save Security</span>
            </Button>
          </div>
        </div>
      </SettingsSection>

      {/* System Settings */}
      <SettingsSection
        title="System Settings"
        description="Configure system-wide settings and preferences"
        icon={<Database className="h-5 w-5 text-blue-600" />}
      >
        <div className="space-y-4">
          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={system.maintenanceMode}
                onChange={(e) => setSystem({ ...system, maintenanceMode: e.target.checked })}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-3 text-sm text-gray-700">Maintenance Mode</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={system.debugMode}
                onChange={(e) => setSystem({ ...system, debugMode: e.target.checked })}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-3 text-sm text-gray-700">Debug Mode</span>
            </label>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                API Rate Limit (requests/hour)
              </label>
              <Input
                type="number"
                value={system.apiRateLimit}
                onChange={(e) => setSystem({ ...system, apiRateLimit: e.target.value })}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max File Size (MB)
              </label>
              <Input
                type="number"
                value={system.maxFileSize}
                onChange={(e) => setSystem({ ...system, maxFileSize: e.target.value })}
              />
            </div>
          </div>
          <div className="flex justify-end">
            <Button onClick={handleSystemSave} className="flex items-center space-x-2">
              <Save className="h-4 w-4" />
              <span>Save System</span>
            </Button>
          </div>
        </div>
      </SettingsSection>
    </div>
  );
};

export default Settings;