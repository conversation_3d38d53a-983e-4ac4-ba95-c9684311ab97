{"name": "@travelease/web-agent", "version": "1.0.0", "description": "TravelEase Travel Agent Portal - React application for travel agents to manage packages and customers", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^5.8.4", "@tanstack/react-table": "^8.10.7", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "zustand": "^4.4.7", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@travelease/shared-types": "workspace:*", "@travelease/shared-ui": "workspace:*", "@travelease/mock-data": "workspace:*"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}}