import { useEffect, useState } from 'react'
import { 
  Package, 
  Users, 
  Calendar, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  MapPin,
  Star
} from 'lucide-react'
import { <PERSON>, Button } from '@travelease/shared-ui'
import { useAuthStore } from '@/store/authStore'

interface DashboardStats {
  totalPackages: number
  activeBookings: number
  totalCustomers: number
  monthlyEarnings: number
  packageGrowth: number
  bookingGrowth: number
  customerGrowth: number
  earningsGrowth: number
}

interface RecentActivity {
  id: string
  type: 'booking' | 'package' | 'customer'
  title: string
  description: string
  timestamp: Date
  amount?: number
}

export default function DashboardPage() {
  const { user } = useAuthStore()
  const [stats, setStats] = useState<DashboardStats>({
    totalPackages: 0,
    activeBookings: 0,
    totalCustomers: 0,
    monthlyEarnings: 0,
    packageGrowth: 0,
    bookingGrowth: 0,
    customerGrowth: 0,
    earningsGrowth: 0,
  })
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Mock data loading - in real app, this would fetch from API
    const loadDashboardData = async () => {
      setIsLoading(true)
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setStats({
        totalPackages: 24,
        activeBookings: 18,
        totalCustomers: 156,
        monthlyEarnings: 45600,
        packageGrowth: 12.5,
        bookingGrowth: 8.3,
        customerGrowth: 15.2,
        earningsGrowth: 22.1,
      })
      
      setRecentActivity([
        {
          id: '1',
          type: 'booking',
          title: 'New Booking - Bali Adventure',
          description: 'Sarah Johnson booked 7-day Bali package',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          amount: 2400,
        },
        {
          id: '2',
          type: 'customer',
          title: 'New Customer Registration',
          description: 'Mike Chen joined as a new customer',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        },
        {
          id: '3',
          type: 'package',
          title: 'Package Updated',
          description: 'Updated pricing for Tokyo Explorer package',
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
        },
        {
          id: '4',
          type: 'booking',
          title: 'Booking Confirmed',
          description: 'Emma Davis confirmed European Grand Tour',
          timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000),
          amount: 5200,
        },
      ])
      
      setIsLoading(false)
    }
    
    loadDashboardData()
  }, [])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours === 1) return '1 hour ago'
    if (diffInHours < 24) return `${diffInHours} hours ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays === 1) return '1 day ago'
    return `${diffInDays} days ago`
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'booking':
        return <Calendar className="h-5 w-5 text-blue-500" />
      case 'package':
        return <Package className="h-5 w-5 text-green-500" />
      case 'customer':
        return <Users className="h-5 w-5 text-purple-500" />
      default:
        return <MapPin className="h-5 w-5 text-gray-500" />
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-gray-200 h-32 rounded-lg"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-200 h-96 rounded-lg"></div>
            <div className="bg-gray-200 h-96 rounded-lg"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Welcome back, {user?.firstName}!
          </h1>
          <p className="text-gray-600">
            Here's what's happening with your travel business today.
          </p>
        </div>
        <Button className="bg-primary-600 hover:bg-primary-700">
          Create New Package
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Packages</p>
              <p className="text-3xl font-bold text-gray-900">{stats.totalPackages}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Package className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            {stats.packageGrowth > 0 ? (
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
            )}
            <span className={`text-sm font-medium ${
              stats.packageGrowth > 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {stats.packageGrowth > 0 ? '+' : ''}{stats.packageGrowth}%
            </span>
            <span className="text-sm text-gray-500 ml-1">from last month</span>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Bookings</p>
              <p className="text-3xl font-bold text-gray-900">{stats.activeBookings}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <Calendar className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            {stats.bookingGrowth > 0 ? (
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
            )}
            <span className={`text-sm font-medium ${
              stats.bookingGrowth > 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {stats.bookingGrowth > 0 ? '+' : ''}{stats.bookingGrowth}%
            </span>
            <span className="text-sm text-gray-500 ml-1">from last month</span>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Customers</p>
              <p className="text-3xl font-bold text-gray-900">{stats.totalCustomers}</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            {stats.customerGrowth > 0 ? (
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
            )}
            <span className={`text-sm font-medium ${
              stats.customerGrowth > 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {stats.customerGrowth > 0 ? '+' : ''}{stats.customerGrowth}%
            </span>
            <span className="text-sm text-gray-500 ml-1">from last month</span>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Monthly Earnings</p>
              <p className="text-3xl font-bold text-gray-900">
                {formatCurrency(stats.monthlyEarnings)}
              </p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-full">
              <DollarSign className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            {stats.earningsGrowth > 0 ? (
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
            )}
            <span className={`text-sm font-medium ${
              stats.earningsGrowth > 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {stats.earningsGrowth > 0 ? '+' : ''}{stats.earningsGrowth}%
            </span>
            <span className="text-sm text-gray-500 ml-1">from last month</span>
          </div>
        </Card>
      </div>

      {/* Recent Activity and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
            <Button variant="outline" size="sm">
              View All
            </Button>
          </div>
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.title}
                  </p>
                  <p className="text-sm text-gray-500">
                    {activity.description}
                  </p>
                  <div className="flex items-center justify-between mt-1">
                    <p className="text-xs text-gray-400">
                      {formatTimeAgo(activity.timestamp)}
                    </p>
                    {activity.amount && (
                      <p className="text-sm font-medium text-green-600">
                        {formatCurrency(activity.amount)}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Quick Actions */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
          <div className="space-y-4">
            <Button className="w-full justify-start" variant="outline">
              <Package className="h-5 w-5 mr-3" />
              Create New Package
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <Users className="h-5 w-5 mr-3" />
              Add New Customer
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <Calendar className="h-5 w-5 mr-3" />
              View Bookings
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <MapPin className="h-5 w-5 mr-3" />
              Build Itinerary
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <DollarSign className="h-5 w-5 mr-3" />
              Check Commission
            </Button>
          </div>
        </Card>
      </div>
    </div>
  )
}