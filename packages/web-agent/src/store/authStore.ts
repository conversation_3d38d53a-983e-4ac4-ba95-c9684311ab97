import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { User } from '@travelease/shared-types'

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  token: string | null
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  setUser: (user: User) => void
}

export const useAuthStore = create<AuthState>()()
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      token: null,

      login: async (email: string, password: string) => {
        try {
          // Mock authentication - in real app, this would call an API
          if (email === '<EMAIL>' && password === 'password') {
            const mockUser: User = {
              id: 'agent-1',
              email: '<EMAIL>',
              firstName: 'John',
              lastName: 'Agent',
              role: 'travel_agent',
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date(),
            }
            
            const mockToken = 'mock-jwt-token-agent'
            
            set({
              user: mockUser,
              isAuthenticated: true,
              token: mockToken,
            })
          } else {
            throw new Error('Invalid credentials')
          }
        } catch (error) {
          throw error
        }
      },

      logout: () => {
        set({
          user: null,
          isAuthenticated: false,
          token: null,
        })
      },

      setUser: (user: User) => {
        set({ user })
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state: AuthState) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        token: state.token,
      }),
    }
  )