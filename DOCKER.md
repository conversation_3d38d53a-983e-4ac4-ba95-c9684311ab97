# Docker Setup for TravelEase Monorepo

This guide explains how to use Docker for development and testing of the TravelEase platform.

## Prerequisites

- Docker Desktop installed and running
- Docker Compose v2.0+
- At least 4GB RAM available for Docker

## Quick Start

### Development Mode

```bash
# Start all services in development mode
pnpm run docker:dev

# Or start in detached mode (background)
pnpm run docker:dev:detached

# Stop all services
pnpm run docker:stop
```

### Production Mode

```bash
# Build and start production services
pnpm run docker:prod

# Clean up everything (containers, volumes, images)
pnpm run docker:clean
```

## Services Overview

### Development Services (Default)

| Service | Port | URL | Description |
|---------|------|-----|-------------|
| `api` | 5000 | http://localhost:5000 | Shared API Backend |
| `web-admin` | 3000 | http://localhost:3000 | Super Admin Portal |
| `web-agent` | 3001 | http://localhost:3001 | Travel Agent Portal |
| `mobile-consumer` | 3002 | http://localhost:3002 | Mobile Consumer App (Web) |

### Production Services

| Service | Port | URL | Description |
|---------|------|-----|-------------|
| `api-prod` | 5001 | http://localhost:5001 | Production API |
| `web-prod` | 8080 | http://localhost:8080 | Production Web Apps |

## Development Workflow

### 1. Initial Setup

```bash
# Clone the repository
git clone <repository-url>
cd travelease

# Start development environment
pnpm run docker:dev
```

### 2. Development with Hot Reload

All services support hot reload in development mode:

- **API Changes**: Edit files in `packages/shared-api/src/` - server restarts automatically
- **Web Admin**: Edit files in `packages/web-admin/src/` - browser refreshes automatically
- **Web Agent**: Edit files in `packages/web-agent/src/` - browser refreshes automatically
- **Mobile App**: Edit files in `packages/mobile-consumer/src/` - Expo reloads automatically

### 3. Accessing Services

- **Super Admin Portal**: http://localhost:3000
- **Travel Agent Portal**: http://localhost:3001
- **Mobile Consumer App**: http://localhost:3002
- **API Documentation**: http://localhost:5000/api-docs (if implemented)
- **API Health Check**: http://localhost:5000/health

### 4. Logs and Debugging

```bash
# View logs for all services
docker-compose logs -f

# View logs for specific service
docker-compose logs -f api
docker-compose logs -f web-admin
docker-compose logs -f web-agent

# Execute commands in running container
docker-compose exec api sh
docker-compose exec web-admin sh
```

## Environment Variables

### Development Environment

- `NODE_ENV=development`
- `VITE_API_URL=http://localhost:5000`
- `EXPO_PUBLIC_API_URL=http://localhost:5000`
- `JWT_SECRET=dev-secret-key`

### Production Environment

- `NODE_ENV=production`
- `JWT_SECRET=prod-secret-key`

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check what's using the ports
   lsof -i :3000
   lsof -i :3001
   lsof -i :5000
   
   # Kill processes if needed
   kill -9 <PID>
   ```

2. **Docker Build Issues**
   ```bash
   # Clean Docker cache
   docker system prune -a
   
   # Rebuild without cache
   docker-compose build --no-cache
   ```

3. **Permission Issues (Linux/macOS)**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   ```

4. **Node Modules Issues**
   ```bash
   # Clean and rebuild
   pnpm run docker:clean
   pnpm run docker:dev
   ```

### Health Checks

```bash
# Check container health
docker-compose ps

# Check API health
curl http://localhost:5000/health

# Check web services
curl http://localhost:3000
curl http://localhost:3001
```

## Production Deployment

### Building for Production

```bash
# Build production images
docker-compose --profile production build

# Start production services
docker-compose --profile production up -d
```

### Production URLs

- **Admin Portal**: http://localhost:8080 (or admin.travelease.local)
- **Agent Portal**: http://localhost:8080 (or agent.travelease.local)
- **API**: http://localhost:5001

### Custom Domains (Optional)

Add to `/etc/hosts` for custom domains:

```
127.0.0.1 admin.travelease.local
127.0.0.1 agent.travelease.local
```

## Performance Optimization

### Development

- Use `.dockerignore` to exclude unnecessary files
- Mount only necessary volumes for hot reload
- Use multi-stage builds to reduce image size

### Production

- Enable gzip compression (configured in nginx)
- Use CDN for static assets
- Implement proper caching headers
- Monitor resource usage

## Security Considerations

- Change default JWT secrets in production
- Use environment-specific configurations
- Implement proper CORS settings
- Use HTTPS in production
- Regular security updates for base images

## Monitoring

```bash
# Monitor resource usage
docker stats

# Monitor logs in real-time
docker-compose logs -f --tail=100

# Check container health
docker-compose ps
```

## Next Steps

1. Set up CI/CD pipeline with Docker
2. Implement proper logging and monitoring
3. Add database services (PostgreSQL, Redis)
4. Configure SSL certificates for production
5. Set up container orchestration (Kubernetes)

For more information, see the main [README.md](./README.md) file.