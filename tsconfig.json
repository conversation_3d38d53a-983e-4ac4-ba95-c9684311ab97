{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES6", "ES2020"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@travelease/shared-types": ["./packages/shared-types/src"], "@travelease/shared-ui": ["./packages/shared-ui/src"], "@travelease/mock-data": ["./packages/mock-data/src"]}}, "files": [], "exclude": ["node_modules", "dist", "build", "**/*.test.ts", "**/*.test.tsx"]}