{"name": "travelease", "version": "1.0.0", "description": "TravelEase - Complete Travel Management Platform", "private": true, "workspaces": ["packages/*"], "packageManager": "pnpm@8.0.0", "scripts": {"dev": "echo 'Development server - Node.js required'", "dev:admin": "echo 'Admin portal - Node.js required'", "dev:agent": "echo 'Agent portal - Node.js required'", "dev:mobile": "echo 'Mobile app - Node.js required'", "dev:api": "echo 'API server - Node.js required'", "build": "echo 'Build all packages - Node.js required'", "setup": "echo 'TravelEase project structure created successfully'"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "typescript": "^5.1.6"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "author": "TravelEase Team", "license": "MIT"}