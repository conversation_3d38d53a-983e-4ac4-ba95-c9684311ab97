{"name": "travelease", "version": "1.0.0", "description": "TravelEase - Complete Travel Management Platform", "private": true, "workspaces": ["packages/*"], "packageManager": "pnpm@8.0.0", "scripts": {"dev": "pnpm install && concurrently \"pnpm dev:api\" \"pnpm dev:admin\" \"pnpm dev:agent\"", "dev:admin": "pnpm --filter @travelease/web-admin dev", "dev:agent": "pnpm --filter @travelease/web-agent dev", "dev:mobile": "pnpm --filter @travelease/mobile-consumer start", "dev:api": "pnpm --filter @travelease/shared-api dev", "build": "pnpm build:shared && pnpm build:apps", "build:shared": "pnpm --filter @travelease/shared-types build && pnpm --filter @travelease/shared-ui build && pnpm --filter @travelease/mock-data build", "build:apps": "pnpm --filter @travelease/shared-api build && pnpm --filter @travelease/web-admin build && pnpm --filter @travelease/web-agent build", "docker:dev": "docker-compose up --build", "docker:dev:detached": "docker-compose up --build -d", "docker:prod": "docker-compose --profile production up --build", "docker:stop": "docker-compose down", "docker:clean": "docker-compose down -v --rmi all", "setup": "echo 'TravelEase project structure created successfully'"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "typescript": "^5.1.6"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "author": "TravelEase Team", "license": "MIT"}