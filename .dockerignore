# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
dist
build
.next
out

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# Documentation
README.md
*.md

# Testing
coverage
.nyc_output
.jest

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Temporary folders
tmp
temp

# ESLint cache
.eslintcache

# TypeScript cache
*.tsbuildinfo

# Expo
.expo
.expo-shared

# React Native
.react-native

# Misc
.cache
.parcel-cache
.sass-cache