# TravelEase Development Environment Configuration

# =============================================================================
# GENERAL CONFIGURATION
# =============================================================================
NODE_ENV=development
APP_NAME=TravelEase
APP_VERSION=1.0.0

# =============================================================================
# API CONFIGURATION
# =============================================================================
API_PORT=3001
API_HOST=localhost
API_URL=http://localhost:3001
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:3002

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
JWT_SECRET=dev-jwt-secret-key-not-for-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
SESSION_SECRET=dev-session-secret-not-for-production

# =============================================================================
# WEB APPLICATION CONFIGURATION
# =============================================================================
VITE_ADMIN_PORT=3000
VITE_ADMIN_API_URL=http://localhost:3001
VITE_ADMIN_APP_ENV=development

VITE_AGENT_PORT=3002
VITE_AGENT_API_URL=http://localhost:3001
VITE_AGENT_APP_ENV=development

# =============================================================================
# MOBILE APPLICATION CONFIGURATION
# =============================================================================
EXPO_PUBLIC_API_URL=http://localhost:3001
EXPO_PUBLIC_APP_ENV=development
EXPO_PUBLIC_APP_NAME=TravelEase

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
ENABLE_MOCK_DATA=true
ENABLE_API_DOCS=true
ENABLE_CORS=true
LOG_LEVEL=debug
LOG_FORMAT=dev

# Hot Reload Configuration
VITE_HMR_PORT=24678
VITE_HMR_HOST=localhost
