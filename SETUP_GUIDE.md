# TravelEase Project Setup Guide

## 🚀 Quick Start

TravelEase is a comprehensive travel management platform built as a monorepo with multiple applications and services.

## 📋 Prerequisites

- **Node.js** >= 18.0.0
- **pnpm** >= 8.0.0
- **Docker** (optional, for containerized development)

## 🏗️ Project Architecture

```
travelease/
├── packages/
│   ├── shared-api/          # Backend API (Express + TypeScript)
│   ├── web-admin/           # Super Admin Portal (React + Vite)
│   ├── web-agent/           # Travel Agent Portal (React + Vite)
│   ├── mobile-consumer/     # Consumer Mobile App (React Native + Expo)
│   ├── shared-types/        # TypeScript type definitions
│   ├── shared-ui/           # Shared UI components
│   └── mock-data/           # Mock data generators
├── docker/                  # Docker configurations
├── docs/                    # Documentation
└── tools/                   # Build and development tools
```

## 🛠️ Installation & Setup

### Method 1: Docker (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd travelease

# Start all services with Docker
pnpm start
# or
docker-compose up --build

# Access the applications:
# - API: http://localhost:5000
# - Admin Portal: http://localhost:3000
# - Agent Portal: http://localhost:3001
# - Mobile App: http://localhost:3002
```

### Method 2: Local Development

```bash
# Install dependencies
pnpm install

# Build shared packages first
pnpm build:shared

# Start all services in development mode
pnpm dev

# Or start services individually:
pnpm dev:api      # API server on port 3001
pnpm dev:admin    # Admin portal on port 3000
pnpm dev:agent    # Agent portal on port 3002
pnpm dev:mobile   # Mobile app on port 3002
```

## 🔧 Available Scripts

### Root Level Scripts
```bash
# Development
pnpm dev                    # Start all services
pnpm dev:api               # Start API server only
pnpm dev:admin             # Start admin portal only
pnpm dev:agent             # Start agent portal only
pnpm dev:mobile            # Start mobile app only

# Building
pnpm build                 # Build all packages
pnpm build:shared          # Build shared packages only
pnpm build:apps            # Build applications only

# Quality & Testing
pnpm lint                  # Lint all packages
pnpm lint:fix              # Fix linting issues
pnpm type-check            # TypeScript type checking
pnpm test                  # Run all tests
pnpm test:watch            # Run tests in watch mode

# Docker
pnpm docker:dev            # Start with Docker
pnpm docker:dev:detached   # Start with Docker (detached)
pnpm docker:prod           # Start production build
pnpm docker:stop           # Stop Docker services
pnpm docker:clean          # Clean Docker containers and images
pnpm docker:logs           # View Docker logs

# Utilities
pnpm clean                 # Clean all node_modules and dist
pnpm clean:build           # Clean build artifacts only
pnpm setup                 # Initial project setup
```

## 🌐 Service URLs

| Service | Development | Production |
|---------|-------------|------------|
| API Server | http://localhost:3001 | https://api.travelease.com |
| Admin Portal | http://localhost:3000 | https://admin.travelease.com |
| Agent Portal | http://localhost:3002 | https://agent.travelease.com |
| Mobile App | http://localhost:3002 | https://app.travelease.com |

## 🔐 Default Login Credentials

### Super Admin
- Email: `<EMAIL>`
- Password: `admin123`

### Travel Agent
- Email: `<EMAIL>`
- Password: `agent123`

### Consumer (Mobile)
- Email: `<EMAIL>`
- Password: `user123`

## 📱 Features by Application

### Super Admin Portal
- System dashboard and analytics
- User management (agents and consumers)
- Travel package oversight
- System configuration
- Audit logs and reports

### Travel Agent Portal
- Agent dashboard with analytics
- Travel package management
- Customer relationship management
- Itinerary builder (drag & drop)
- Booking management
- Communication hub

### Mobile Consumer App
- Trip dashboard
- Detailed itineraries
- Real-time notifications
- Travel assistant
- Document wallet
- Offline capabilities

## 🔧 Environment Configuration

Copy the environment files and customize as needed:

```bash
# Root environment
cp .env.example .env

# API environment
cp packages/shared-api/.env.example packages/shared-api/.env

# Web applications
cp packages/web-admin/.env.example packages/web-admin/.env
cp packages/web-agent/.env.example packages/web-agent/.env
```

## 🐛 Troubleshooting

### Common Issues

1. **pnpm install fails with network errors**
   ```bash
   # Try using Docker instead
   pnpm docker:dev
   ```

2. **Port conflicts**
   ```bash
   # Check what's running on ports
   lsof -i :3000 -i :3001 -i :3002
   # Kill processes if needed
   kill -9 <PID>
   ```

3. **Build failures**
   ```bash
   # Clean and rebuild
   pnpm clean
   pnpm install
   pnpm build:shared
   ```

4. **Docker build issues**
   ```bash
   # Clean Docker cache
   docker system prune -a
   pnpm docker:clean
   ```

## 📚 Development Workflow

1. **Start Development**
   ```bash
   pnpm dev
   ```

2. **Make Changes**
   - Edit files in respective packages
   - Hot reload is enabled for all services

3. **Testing**
   ```bash
   pnpm test
   pnpm lint
   pnpm type-check
   ```

4. **Building for Production**
   ```bash
   pnpm build
   pnpm docker:prod
   ```

## 🏗️ Technology Stack

- **Frontend**: React 18, TypeScript, Vite, Tailwind CSS
- **Backend**: Node.js, Express, TypeScript
- **Mobile**: React Native, Expo
- **State Management**: Zustand, React Query
- **UI Components**: Custom design system
- **Development**: pnpm workspaces, Docker, ESLint, Prettier
- **Data**: Mock data with faker.js (ready for real database integration)

## 📖 Next Steps

1. **Database Integration**: Replace mock data with real database
2. **Authentication**: Implement proper JWT authentication
3. **Payment Integration**: Add payment gateway
4. **External APIs**: Integrate weather, currency, and map services
5. **Testing**: Add comprehensive test coverage
6. **Deployment**: Set up CI/CD pipeline

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details
