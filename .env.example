# TravelEase Environment Configuration Template
# Copy this file to .env and update the values for your environment

# =============================================================================
# GENERAL CONFIGURATION
# =============================================================================
NODE_ENV=development
APP_NAME=TravelEase
APP_VERSION=1.0.0

# =============================================================================
# API CONFIGURATION
# =============================================================================
# API Server Configuration
API_PORT=3001
API_HOST=localhost
API_URL=http://localhost:3001

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:3002

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Session Configuration
SESSION_SECRET=your-session-secret-change-this-in-production

# =============================================================================
# WEB APPLICATION CONFIGURATION
# =============================================================================
# Admin Portal Configuration
VITE_ADMIN_PORT=3000
VITE_ADMIN_API_URL=http://localhost:3001
VITE_ADMIN_APP_ENV=development

# Agent Portal Configuration
VITE_AGENT_PORT=3002
VITE_AGENT_API_URL=http://localhost:3001
VITE_AGENT_APP_ENV=development

# =============================================================================
# MOBILE APPLICATION CONFIGURATION
# =============================================================================
# Mobile App Configuration
EXPO_PUBLIC_API_URL=http://localhost:3001
EXPO_PUBLIC_APP_ENV=development
EXPO_PUBLIC_APP_NAME=TravelEase

# =============================================================================
# DATABASE CONFIGURATION (Future Use)
# =============================================================================
# Database Configuration (when moving from mock data)
# DATABASE_URL=postgresql://username:password@localhost:5432/travelease
# DATABASE_HOST=localhost
# DATABASE_PORT=5432
# DATABASE_NAME=travelease
# DATABASE_USER=travelease_user
# DATABASE_PASSWORD=your_database_password

# =============================================================================
# EXTERNAL SERVICES (Future Integration)
# =============================================================================
# Email Service Configuration
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# WhatsApp Integration
# WHATSAPP_API_URL=https://api.whatsapp.com
# WHATSAPP_API_KEY=your-whatsapp-api-key

# Payment Gateway
# STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
# STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key

# Weather API
# WEATHER_API_KEY=your-weather-api-key
# WEATHER_API_URL=https://api.openweathermap.org/data/2.5

# Currency Exchange API
# CURRENCY_API_KEY=your-currency-api-key
# CURRENCY_API_URL=https://api.exchangerate-api.com/v4

# =============================================================================
# LOGGING & MONITORING
# =============================================================================
# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined

# Monitoring (Future Use)
# SENTRY_DSN=your-sentry-dsn
# ANALYTICS_ID=your-analytics-id

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# Development Configuration
ENABLE_MOCK_DATA=true
ENABLE_API_DOCS=true
ENABLE_CORS=true

# Hot Reload Configuration
VITE_HMR_PORT=24678
VITE_HMR_HOST=localhost
