# Multi-stage Dockerfile for TravelEase monorepo

# Base Node.js image
FROM node:18-alpine AS base

# Install pnpm globally
RUN npm install -g pnpm@8

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml* pnpm-workspace.yaml ./
COPY packages/shared-api/package.json ./packages/shared-api/
COPY packages/shared-types/package.json ./packages/shared-types/
COPY packages/shared-ui/package.json ./packages/shared-ui/
COPY packages/mock-data/package.json ./packages/mock-data/
COPY packages/web-admin/package.json ./packages/web-admin/
COPY packages/web-agent/package.json ./packages/web-agent/
COPY packages/mobile-consumer/package.json ./packages/mobile-consumer/

# Install dependencies
RUN pnpm install

# Copy source code
COPY . .

# Build shared packages first
RUN pnpm run build:shared

# Development stage
FROM base AS development
EXPOSE 3000 3001 3002 5000
CMD ["pnpm", "run", "dev"]

# Production build stage
FROM base AS build

# Build all packages
RUN pnpm run build

# Production stage for API
FROM node:18-alpine AS api-production
WORKDIR /app
RUN npm install -g pnpm@8

# Copy built API
COPY --from=build /app/packages/shared-api/dist ./packages/shared-api/dist
COPY --from=build /app/packages/shared-api/package.json ./packages/shared-api/
COPY --from=build /app/packages/shared-types/dist ./packages/shared-types/dist
COPY --from=build /app/packages/shared-types/package.json ./packages/shared-types/
COPY --from=build /app/packages/mock-data/dist ./packages/mock-data/dist
COPY --from=build /app/packages/mock-data/package.json ./packages/mock-data/
COPY --from=build /app/package.json ./
COPY --from=build /app/pnpm-workspace.yaml ./

# Install production dependencies
RUN pnpm install --prod

EXPOSE 5000
CMD ["node", "packages/shared-api/dist/index.js"]

# Production stage for web apps
FROM nginx:alpine AS web-production

# Copy built web applications
COPY --from=build /app/packages/web-admin/dist /usr/share/nginx/html/admin
COPY --from=build /app/packages/web-agent/dist /usr/share/nginx/html/agent

# Copy nginx configuration
COPY docker/nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]