# TravelEase Production Environment Configuration

# =============================================================================
# GENERAL CONFIGURATION
# =============================================================================
NODE_ENV=production
APP_NAME=TravelEase
APP_VERSION=1.0.0

# =============================================================================
# API CONFIGURATION
# =============================================================================
API_PORT=5000
API_HOST=0.0.0.0
API_URL=https://api.travelease.com
CORS_ORIGIN=https://admin.travelease.com,https://agent.travelease.com,https://app.travelease.com

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
# IMPORTANT: Change these values in production!
JWT_SECRET=CHANGE_THIS_IN_PRODUCTION_SUPER_SECRET_KEY
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d
SESSION_SECRET=CHANGE_THIS_IN_PRODUCTION_SESSION_SECRET

# =============================================================================
# WEB APPLICATION CONFIGURATION
# =============================================================================
VITE_ADMIN_API_URL=https://api.travelease.com
VITE_ADMIN_APP_ENV=production

VITE_AGENT_API_URL=https://api.travelease.com
VITE_AGENT_APP_ENV=production

# =============================================================================
# MOBILE APPLICATION CONFIGURATION
# =============================================================================
EXPO_PUBLIC_API_URL=https://api.travelease.com
EXPO_PUBLIC_APP_ENV=production
EXPO_PUBLIC_APP_NAME=TravelEase

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================
ENABLE_MOCK_DATA=false
ENABLE_API_DOCS=false
ENABLE_CORS=true
LOG_LEVEL=warn
LOG_FORMAT=combined

# Security Headers
HELMET_ENABLED=true
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
